%------------------------------------------------------------------------
% AIS Receiver with Hybrid Viterbi MLSD (Bit Decision Only)
% 비트 결정만 Viterbi MLSD, 나머지는 기존 방식 유지
% Based on: ais_receiver_collation_with_real_data_250712.m
% Created: 2024-07-14
%------------------------------------------------------------------------

clear all;
close all;

%------------------------------------------------------------------------
% 설정 파라미터
%------------------------------------------------------------------------
ENABLE_VITERBI_BIT_DECISION = 0;            % 0: 기존 방식, 1: Viterbi 비트 결정
ENABLE_PLOT1            = 0;                % 0: Disable, 1: Enable
ENABLE_PLOT2            = 0;                % 0: Disable, 1: Enable  
ENABLE_PLOT3            = 1;                % 0: Disable, 1: Enable (성공 패킷)
ENABLE_PLOT99           = 1;                % 0: Disable, 1: Enable (오류 패킷)
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: Enable

% Viterbi 비트 결정 파라미터
VITERBI_WINDOW_SIZE = 16;                   % Viterbi 윈도우 크기
VITERBI_LOOKAHEAD = 8;                      % 미래 샘플 참조 개수

%------------------------------------------------------------------------
% 상수 정의 (기존과 동일)
%------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_PRELOAD   = 1;
RX_MDM_STATUS_DATA      = 2;

SYNC_DETECT_OFFSET      = 24;
DC_MID_LEVEL           = 0;

%------------------------------------------------------------------------
% 전역 변수 초기화 (기존과 동일)
%------------------------------------------------------------------------
G_dSyncDetCnt    = 0;
G_dAdcErrCnt     = 0;
G_dStartErrCnt   = 0;
G_dStuffErrCnt   = 0;
G_dCrcErrCnt     = 0;
G_dRcvPktCnt     = 0;

G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
G_wRxShiftReg    = 0;
G_wRxBitCount    = 0;
G_bRxByteData    = 0;
G_wCrcRegData    = 0xffff;
G_wRxReferValue  = DC_MID_LEVEL;
G_PreStart       = 1;
G_PreOffset      = 100;

% 데이터 배열
G_BitDataArray   = zeros(1, 1000);
G_dCRCErrSymIdx  = zeros(1, 1000);

% Viterbi 비트 결정 통계
G_ViterbiBitStats = struct();
G_ViterbiBitStats.total_decisions = 0;
G_ViterbiBitStats.viterbi_decisions = 0;
G_ViterbiBitStats.confidence_sum = 0;

%------------------------------------------------------------------------
% 데이터 로드 (기존과 동일)
%------------------------------------------------------------------------
fprintf('AIS 데이터 로딩 중...\n');

% 데이터 파일 읽기
fid = fopen('DumpData/DUMPDATA_250525_ch2.bin', 'rb');
if fid == -1
    error('데이터 파일을 열 수 없습니다: DumpData/DUMPDATA_250525_ch2.bin');
end

G_pSrcDataCh1 = fread(fid, inf, 'int16');
fclose(fid);

if isempty(G_pSrcDataCh1)
    error('데이터 파일이 비어있습니다.');
end

fprintf('데이터 로드 완료: %d 샘플\n', length(G_pSrcDataCh1));

% 데이터 정규화
G_pSrcDataCh1 = double(G_pSrcDataCh1) / 32768.0;

%------------------------------------------------------------------------
% 필터 초기화 (기존과 동일)
%------------------------------------------------------------------------
if (ENABLE_GMSK_RX_FLT > 0)
    % GMSK 수신 필터 설정
    filter_taps = 64;
    G_pFilteredData = filter(ones(1, filter_taps)/filter_taps, 1, G_pSrcDataCh1);
    fprintf('GMSK 수신 필터 적용 완료\n');
else
    G_pFilteredData = G_pSrcDataCh1;
end

%------------------------------------------------------------------------
% Viterbi 비트 결정 초기화
%------------------------------------------------------------------------
if ENABLE_VITERBI_BIT_DECISION == 1
    fprintf('Viterbi 비트 결정 모드 활성화\n');
    
    % Viterbi 상태 버퍼
    viterbi_signal_buffer = zeros(1, VITERBI_WINDOW_SIZE + VITERBI_LOOKAHEAD);
    viterbi_buffer_idx = 1;
    
    % 비트 결정 히스토리
    bit_decision_history = zeros(1, VITERBI_WINDOW_SIZE);
    
    fprintf('Viterbi 윈도우 크기: %d, 미래 참조: %d\n', VITERBI_WINDOW_SIZE, VITERBI_LOOKAHEAD);
else
    fprintf('기존 비트 결정 모드 사용\n');
end

%------------------------------------------------------------------------
% 메인 처리 루프 (기존 구조 유지)
%------------------------------------------------------------------------
fprintf('\n=== 하이브리드 Viterbi AIS 수신기 시작 ===\n');
fprintf('전체 샘플 수: %d\n', length(G_pSrcDataCh1));

nCurSymbolIdx = 1;
nDataLength = length(G_pSrcDataCh1);

% 동적 DC 오프셋 계산용 (기존과 동일)
AdativeDcOffset = zeros(1, nDataLength);
BitArray = zeros(1, nDataLength);

% 메인 처리 루프
for idx = 1:nDataLength
    if mod(idx, 100000) == 0
        fprintf('처리 진행률: %.1f%%\n', (idx/nDataLength)*100);
    end
    
    % 현재 신호값
    if (ENABLE_GMSK_RX_FLT > 0)
        current_signal = G_pFilteredData(idx);
    else
        current_signal = G_pSrcDataCh1(idx);
    end
    
    % 동적 DC 오프셋 계산 (기존과 동일)
    if idx > 100
        AdativeDcOffset(idx) = mean(G_pFilteredData(max(1, idx-50):idx));
    else
        AdativeDcOffset(idx) = mean(G_pFilteredData(1:idx));
    end
    
    % ★ 핵심: 비트 결정 (Viterbi vs 기존)
    if ENABLE_VITERBI_BIT_DECISION == 1
        % Viterbi MLSD 비트 결정
        [bit_decision, confidence] = viterbi_bit_decision(idx, current_signal, AdativeDcOffset(idx));
        BitArray(idx) = bit_decision;
        
        % 통계 업데이트
        G_ViterbiBitStats.total_decisions = G_ViterbiBitStats.total_decisions + 1;
        if confidence > 0.5  % 높은 신뢰도로 결정된 경우
            G_ViterbiBitStats.viterbi_decisions = G_ViterbiBitStats.viterbi_decisions + 1;
        end
        G_ViterbiBitStats.confidence_sum = G_ViterbiBitStats.confidence_sum + confidence;
    else
        % 기존 방식 비트 결정
        if current_signal > AdativeDcOffset(idx)
            BitArray(idx) = 1;
        else
            BitArray(idx) = 0;
        end
    end
    
    % ★ 나머지 처리는 기존과 완전 동일
    % 동기 검출, 데이터 수신, stuff bit, CRC 등 모든 로직 유지
    
    % 상태별 처리 (기존과 동일)
    switch G_wRxRunStatus
        case RX_MDM_STATUS_PREAMBLE
            % 프리앰블 및 동기 패턴 검출 (기존 로직)
            [sync_detected, nCurSymbolIdx] = detect_sync_pattern_original(idx, BitArray);
            
            if sync_detected
                G_dSyncDetCnt = G_dSyncDetCnt + 1;
                
                % 패킷 시작 위치 설정
                if (nCurSymbolIdx > G_PreOffset)
                    G_PreStart = nCurSymbolIdx - G_PreOffset;
                else
                    G_PreStart = 1;
                end
                
                fprintf('동기 패턴 검출 #%d (위치: %d)\n', G_dSyncDetCnt, nCurSymbolIdx);
                
                % 데이터 수신 상태로 전환
                G_wRxRunStatus = RX_MDM_STATUS_DATA;
                G_wRxBitCount = 0;
                G_bRxByteData = 0;
                G_wCrcRegData = 0xffff;
            end
            
        case RX_MDM_STATUS_DATA
            % 데이터 수신 처리 (기존 로직 완전 동일)

            % 시프트 레지스터 업데이트
            G_wRxShiftReg = bitor(bitshift(bitand(G_wRxShiftReg, 0x00ff), 1), BitArray(idx));

            % Stuff bit 검출 및 처리
            if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                G_wRxBitCount = G_wRxBitCount + 1;

                % 최대 비트 수 체크
                if(G_wRxBitCount >= 300)  % 임시 최대값
                    % Stuffing bit 오류
                    if (ENABLE_PLOT99 == 1)
                        h_fig99 = figure(99);
                        h_fig99.Name = 'Stuffing Bit Error';
                        x1 = G_PreStart:idx;
                        plot(x1, G_pFilteredData(x1), '-r'); grid;
                        title('Error Stuffing Bit');
                    end

                    % 상태 리셋
                    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                    G_wRxShiftReg  = 0;
                    G_bRxByteData = 0;
                    G_wRxReferValue= DC_MID_LEVEL;
                    G_dStuffErrCnt = G_dStuffErrCnt + 1;
                    continue;
                end

                % 비트 데이터 추출
                G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));
                G_BitDataArray(G_wRxBitCount) = G_wNewBitData;

                % CRC 업데이트
                G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
            end

            % 종료 플래그 검출 (0x7E)
            if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                if(G_wCrcRegData == 0xf0b8)  % CRC 정상
                    % 성공 패킷 처리
                    if (ENABLE_PLOT3 == 1)
                        h_fig3 = figure(3);
                        h_fig3.Name = 'Received Data(CRC OK)';
                        x1 = G_PreStart:idx+50;
                        if (G_PreStart <= SYNC_DETECT_OFFSET)
                            x2 = G_PreStart:nCurSymbolIdx;
                            x3 = G_PreStart:nCurSymbolIdx;
                        else
                            x2 = G_PreStart:idx;
                            x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                        end
                        plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid;
                        title('Hybrid Viterbi - Received AIS Packet');
                    end

                    % 상태 리셋
                    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                    G_wRxShiftReg  = 0;
                    G_wRxReferValue= DC_MID_LEVEL;
                    G_bRxByteData = 0;
                    G_dRcvPktCnt = G_dRcvPktCnt + 1;

                    fprintf('✓ 패킷 수신 성공 #%d (Viterbi 비트 결정)\n', G_dRcvPktCnt);
                else
                    % CRC 오류 처리
                    if (ENABLE_PLOT99 == 1)
                        h_fig99 = figure(99);
                        h_fig99.Name = 'Received Data(CRC ERROR)';
                        x1 = G_PreStart:idx;
                        if (G_PreStart <= SYNC_DETECT_OFFSET)
                            x2 = G_PreStart:nCurSymbolIdx;
                            x3 = G_PreStart:nCurSymbolIdx;
                        else
                            x2 = G_PreStart:idx;
                            x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                        end
                        plot(x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid;
                        title('Hybrid Viterbi - CRC Error Packet');
                    end

                    % 상태 리셋
                    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                    G_wRxShiftReg  = 0;
                    G_wRxReferValue= DC_MID_LEVEL;
                    G_bRxByteData = 0;
                    G_dCrcErrCnt = G_dCrcErrCnt + 1;
                    G_dCRCErrSymIdx(G_dCrcErrCnt) = G_PreStart;
                end
            end
            
        otherwise
            % 기타 상태 처리
    end
end

%------------------------------------------------------------------------
% 결과 출력
%------------------------------------------------------------------------
fprintf('\n=== AIS 수신기 성능 결과 ===\n');
fprintf('동기 검출: %d개\n', G_dSyncDetCnt);
fprintf('ADC 오류: %d개\n', G_dAdcErrCnt);
fprintf('시작 비트 오류: %d개\n', G_dStartErrCnt);
fprintf('스터핑 비트 오류: %d개\n', G_dStuffErrCnt);
fprintf('CRC 오류: %d개\n', G_dCrcErrCnt);
fprintf('성공 패킷: %d개\n', G_dRcvPktCnt);

if ENABLE_VITERBI_BIT_DECISION == 1
    fprintf('\n=== Viterbi 비트 결정 성능 ===\n');
    fprintf('총 비트 결정: %d개\n', G_ViterbiBitStats.total_decisions);
    fprintf('Viterbi 고신뢰도 결정: %d개\n', G_ViterbiBitStats.viterbi_decisions);
    if G_ViterbiBitStats.total_decisions > 0
        avg_confidence = G_ViterbiBitStats.confidence_sum / G_ViterbiBitStats.total_decisions;
        viterbi_ratio = (G_ViterbiBitStats.viterbi_decisions / G_ViterbiBitStats.total_decisions) * 100;
        fprintf('평균 신뢰도: %.3f\n', avg_confidence);
        fprintf('Viterbi 활용률: %.1f%%\n', viterbi_ratio);
    end
end

%------------------------------------------------------------------------
% 성능 그래프
%------------------------------------------------------------------------
figure(9);
bar_x = ["SyncDet" "AdcErr" "StartErr" "StuffErr" "CrcErr" "Packet OK"];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y, 'FaceColor', 'flat');
b.CData(6,:) = [0.6350 0.0780 0.1840];
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')

if ENABLE_VITERBI_BIT_DECISION == 1
    title('AIS Receiver with Hybrid Viterbi Bit Decision');
else
    title('AIS Receiver with Original Bit Decision');
end

fprintf('\n=== 처리 완료 ===\n');

%------------------------------------------------------------------------
% Viterbi 비트 결정 함수 (핵심 개선 부분)
%------------------------------------------------------------------------
function [bit_decision, confidence] = viterbi_bit_decision(current_idx, current_signal, dc_offset)
    global G_pFilteredData VITERBI_WINDOW_SIZE VITERBI_LOOKAHEAD;

    % 기본값
    bit_decision = (current_signal > dc_offset);
    confidence = 0.5;

    % 충분한 데이터가 있을 때만 Viterbi 적용
    window_size = 16;  % VITERBI_WINDOW_SIZE 직접 사용
    lookahead = 8;     % VITERBI_LOOKAHEAD 직접 사용
    data_length = length(G_pFilteredData);

    if current_idx < window_size
        return;
    end
    if current_idx > (data_length - lookahead)
        return;
    end

    % 신호 윈도우 추출
    start_idx = current_idx - window_size + 1;
    end_idx = current_idx + lookahead;
    signal_window = G_pFilteredData(start_idx:end_idx);

    % 신호 정규화
    window_mean = mean(signal_window);
    window_std = std(signal_window);
    if window_std > 0.01
        normalized_signal = (signal_window - window_mean) / window_std;
    else
        normalized_signal = signal_window - window_mean;
    end

    % 2-상태 Viterbi MLSD (간단한 버전)
    num_states = 2;  % [Low, High]
    window_length = length(normalized_signal);

    % 경로 메트릭 초기화
    path_metrics = [0, inf];  % [상태0(Low), 상태1(High)]

    % Viterbi 알고리즘
    for t = 1:window_length
        new_metrics = [inf, inf];
        current_sample = normalized_signal(t);

        % 각 상태에서 각 출력에 대한 메트릭 계산
        for prev_state = 1:num_states
            if path_metrics(prev_state) < inf
                for output_bit = 0:1
                    % 예상 신호 레벨
                    if output_bit == 0
                        expected_level = -0.5;  % Low
                        next_state = 1;
                    else
                        expected_level = 0.5;   % High
                        next_state = 2;
                    end

                    % 브랜치 메트릭 (신호와 예상 레벨의 차이)
                    error = current_sample - expected_level;
                    branch_metric = error^2;

                    % NRZI 특성 고려 (상태 전이 제약)
                    if prev_state == next_state
                        % 같은 상태 유지 (NRZI: 0)
                        nrzi_bit = 0;
                    else
                        % 상태 변화 (NRZI: 1)
                        nrzi_bit = 1;
                    end

                    total_metric = path_metrics(prev_state) + branch_metric;

                    if total_metric < new_metrics(next_state)
                        new_metrics(next_state) = total_metric;

                        % 현재 시점이 결정 지점이면 비트 저장
                        if t == window_size
                            bit_decision = nrzi_bit;
                        end
                    end
                end
            end
        end

        path_metrics = new_metrics;
    end

    % 신뢰도 계산
    [min_metric, ~] = min(path_metrics);
    if min_metric < inf
        confidence = exp(-min_metric / window_length);
        confidence = max(0.1, min(0.9, confidence));
    else
        confidence = 0.1;
    end

    % 신뢰도가 낮으면 기존 방식 사용
    if confidence < 0.3
        bit_decision = (current_signal > dc_offset);
        confidence = 0.3;
    end
end

%------------------------------------------------------------------------
% 기존 동기 패턴 검출 함수
%------------------------------------------------------------------------
function [sync_detected, sync_position] = detect_sync_pattern_original(current_idx, bit_array)
    sync_detected = false;
    sync_position = current_idx;

    % AIS 동기 패턴: 01111110 (0x7E)
    sync_pattern = [0 1 1 1 1 1 1 0];
    pattern_length = length(sync_pattern);

    if current_idx >= pattern_length
        % 현재 위치에서 패턴 매칭
        current_pattern = bit_array(current_idx-pattern_length+1:current_idx);

        % 패턴 일치도 계산
        match_count = sum(current_pattern == sync_pattern);
        match_ratio = match_count / pattern_length;

        % 75% 이상 일치하면 동기 패턴으로 인식
        if match_ratio >= 0.75
            sync_detected = true;
            sync_position = current_idx;
        end
    end
end

%------------------------------------------------------------------------
% CRC 업데이트 함수 (기존과 동일)
%------------------------------------------------------------------------
function crc_out = update_crc(crc_in, data_bit)
    % AIS CRC-16 계산
    crc_out = crc_in;

    if bitand(bitxor(bitshift(crc_out, -15), data_bit), 0x0001)
        crc_out = bitxor(bitshift(bitand(crc_out, 0x7fff), 1), 0x1021);
    else
        crc_out = bitshift(bitand(crc_out, 0x7fff), 1);
    end

    crc_out = bitand(crc_out, 0xffff);
end
