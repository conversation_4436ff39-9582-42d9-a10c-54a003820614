clear;

%------------------------------------------------------------------------
% AIS Receiver with PLL + Viterbi MLSD Integration
% 기존 PLL 타이밍 복구 + Viterbi MLSD 비트 결정
% Based on: ais_receiver_collation_with_real_data_250712.m
% Created: 2024-07-14
%------------------------------------------------------------------------

%-------------------------------------------------------------------------
% 설정 파라미터 (기존과 동일)
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data
ENABLE_NOTCH_FLT        = 1;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 1;                % 0: Disable, 1: Adaptive DC Offset
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable

% ★ Viterbi MLSD 설정
ENABLE_VITERBI_MLSD     = 1;                % 0: 기존 방식, 1: Viterbi MLSD 적용
VITERBI_WINDOW_SIZE     = 8;                % Viterbi 윈도우 크기 (비트 단위)
VITERBI_CONFIDENCE_THRESHOLD = 0.3;         % 신뢰도 임계값

%-------------------------------------------------------------------------
ENABLE_DEBUG            = 0;
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Received Data packet with CRC
else
    ENABLE_PLOT1        = 0;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 0;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 성공 패킷은 항상 표시
end

ENABLE_DEBUG_ERROR      = 0;
if (ENABLE_DEBUG_ERROR == 1)
    ENABLE_PLOT96       = 1;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 1;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 1;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % 0: Disable, 1: CRC Error
else
    ENABLE_PLOT96       = 0;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 0;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 0;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % CRC 오류는 항상 표시
end

%-------------------------------------------------------------------------
% 기존 파라미터 유지
%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = .750;
MAX_SYNC_COUNT          = 25;

%-------------------------------------------------------------------------
% PLL 파라미터 (기존과 동일)
%-------------------------------------------------------------------------
RX_PLL_FULL             = OSR * 256;
RX_PLL_HALF             = RX_PLL_FULL / 2;
RX_PLL_INCR             = 256;
RX_PLL_STEP             = 32;

%-------------------------------------------------------------------------
% 상수 정의 (기존과 동일)
%-------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_PRELOAD   = 1;
RX_MDM_STATUS_DATA      = 2;
RX_MDM_STATUS_START     = 3;

SYNC_DETECT_OFFSET      = 24;
DC_MID_LEVEL           = 0;

%-------------------------------------------------------------------------
% 전역 변수 초기화 (기존과 동일)
%-------------------------------------------------------------------------
G_dSyncDetCnt    = 0;
G_dAdcErrCnt     = 0;
G_dStartErrCnt   = 0;
G_dStuffErrCnt   = 0;
G_dCrcErrCnt     = 0;
G_dRcvPktCnt     = 0;

G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
G_wRxShiftReg    = 0;
G_wRxBitCount    = 0;
G_bRxByteData    = 0;
G_wCrcRegData    = 0xffff;
G_wRxReferValue  = DC_MID_LEVEL;
G_PreStart       = 1;
G_PreOffset      = 100;

% PLL 관련 변수 (기존과 동일)
G_dSwRxPllValue  = 0;
G_dSwRxPllSampC  = 0;
G_dSwRxPllSampP  = 0;
G_dSwRxPllCntrX  = 1;
G_wRxNrziCntr    = 1;
G_wRxCurrBitD    = 0;
G_wRxPrevBitD    = 0;
G_wRxAfAdcData   = 0;
G_wRxNrziCurr    = 0;

% 신호 품질 관련
G_SignalQuality  = 2.0;

% 데이터 배열
G_BitDataArray   = zeros(1, 1000);
G_dCRCErrSymIdx  = zeros(1, 1000);

% ★ Viterbi MLSD 관련 변수
G_ViterbiBuffer  = zeros(1, VITERBI_WINDOW_SIZE);
G_ViterbiIndex   = 1;
G_ViterbiStats   = struct();
G_ViterbiStats.total_decisions = 0;
G_ViterbiStats.viterbi_decisions = 0;
G_ViterbiStats.confidence_sum = 0;

% ★ 동기 검출 관련 변수 (기존과 동일)
G_dMaxSyncCorrel = 0;
G_dMaxSyncCnt = 0;
G_dSyncSymbolIndex = 1;
G_dStartSymbolIndex = 1;
G_CorrelHistory = zeros(1, 50);
G_SignalQuality = 2.0;

% 프리앰블 패턴 (기존과 동일)
preamble_pattern = [0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1];  % 24비트 프리앰블
G_vGmskPreamble = preamble_pattern;

%------------------------------------------------------------------------
% 데이터 로드 (기존과 동일)
%------------------------------------------------------------------------
fprintf('AIS 데이터 로딩 중...\n');

% 데이터 파일 읽기
fid = fopen('DumpData/DUMPDATA_250525_ch2.bin', 'rb');
if fid == -1
    error('데이터 파일을 열 수 없습니다: DumpData/DUMPDATA_250525_ch2.bin');
end

G_pSrcDataCh1 = fread(fid, inf, 'int16');
fclose(fid);

if isempty(G_pSrcDataCh1)
    error('데이터 파일이 비어있습니다.');
end

fprintf('데이터 로드 완료: %d 샘플\n', length(G_pSrcDataCh1));

% 데이터 정규화
G_pSrcDataCh1 = double(G_pSrcDataCh1) / 32768.0;

%------------------------------------------------------------------------
% 필터 초기화 (기존과 동일)
%------------------------------------------------------------------------
if (ENABLE_GMSK_RX_FLT > 0)
    % GMSK 수신 필터 설정
    filter_taps = 64;
    G_pFilteredData = filter(ones(1, filter_taps)/filter_taps, 1, G_pSrcDataCh1);
    fprintf('GMSK 수신 필터 적용 완료\n');
else
    G_pFilteredData = G_pSrcDataCh1;
end

%------------------------------------------------------------------------
% 메인 처리 루프 (PLL + Viterbi MLSD)
%------------------------------------------------------------------------
fprintf('\n=== PLL + Viterbi MLSD AIS 수신기 시작 ===\n');
fprintf('전체 샘플 수: %d\n', length(G_pSrcDataCh1));

nDataLength = length(G_pSrcDataCh1);

% 동적 DC 오프셋 계산용
AdativeDcOffset = zeros(1, nDataLength);
BitArray = zeros(1, nDataLength);

% 메인 처리 루프
for idx = 1:nDataLength
    if mod(idx, 100000) == 0
        fprintf('처리 진행률: %.1f%%\n', (idx/nDataLength)*100);
    end
    
    % 현재 신호값
    if (ENABLE_GMSK_RX_FLT > 0)
        G_wRxAfAdcData = G_pFilteredData(idx);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(idx);
    end
    
    % 동적 DC 오프셋 계산
    if idx > 100
        AdativeDcOffset(idx) = mean(G_pFilteredData(max(1, idx-50):idx));
    else
        AdativeDcOffset(idx) = mean(G_pFilteredData(1:idx));
    end
    
    % ★ 기존 PLL 타이밍 복구 로직 (그대로 유지)
    G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
    G_dSwRxPllSampC = G_wRxNrziCurr;

    % PLL 카운터 로직 (수신율 향상 코드)
    if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
        if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
            G_wRxNrziCntr = G_wRxNrziCntr + 1;
            G_dSwRxPllSampC = G_dSwRxPllSampP;
        else
            G_wRxNrziCntr = 1;
        end
    else
        G_wRxNrziCntr = G_wRxNrziCntr + 1;
    end

    % PLL 값 조정
    if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
        if (G_wRxRunStatus == RX_MDM_STATUS_START)
            if (G_dSwRxPllCntrX >= (OSR * 2 - 3) && G_dSwRxPllCntrX <= (OSR * 2 + 3))
                G_dSwRxPllValue = RX_PLL_HALF;
            end
        end

        if (G_dSwRxPllValue < RX_PLL_HALF)
            G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
        else
            G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
        end

        G_dSwRxPllCntrX = 1;
    else
        G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
    end

    G_dSwRxPllSampP = G_dSwRxPllSampC;

    G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
    if(G_dSwRxPllValue >= RX_PLL_FULL)
        G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
    else
        continue;
    end

    % ★ 핵심: Viterbi MLSD 비트 결정 (기존 G_wRxCurrBitD = G_dSwRxPllSampC 대체)
    if ENABLE_VITERBI_MLSD == 1
        [G_wRxCurrBitD, viterbi_confidence] = viterbi_mlsd_bit_decision(G_dSwRxPllSampC, idx);
        
        % 통계 업데이트
        G_ViterbiStats.total_decisions = G_ViterbiStats.total_decisions + 1;
        if viterbi_confidence > VITERBI_CONFIDENCE_THRESHOLD
            G_ViterbiStats.viterbi_decisions = G_ViterbiStats.viterbi_decisions + 1;
        end
        G_ViterbiStats.confidence_sum = G_ViterbiStats.confidence_sum + viterbi_confidence;
    else
        % 기존 방식
        G_wRxCurrBitD = G_dSwRxPllSampC;
    end

    % ★ 기존 NRZI 디코딩 및 데이터 처리 (그대로 유지)
    G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
    if (G_wRxCurrBitD == G_wRxPrevBitD)
        G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
        BitArray(idx) = 0.05;
    else
        G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
        BitArray(idx) = 0.015;

        if (ENABLE_ADAPT_DC_OFFSET == 1)
            % 개선된 적응형 DC Offset 추적
            if G_SignalQuality > 2.5
                alpha_dc = 0.900;
            elseif G_SignalQuality > 1.5
                alpha_dc = 0.850;
            else
                alpha_dc = 0.800;
            end

            % 반전 구간에서만 DC Offset 보정
            if (ENABLE_GMSK_RX_FLT > 0)
                sample = G_pFilteredData(idx);
            else
                sample = G_pSrcDataCh1(idx);
            end
            G_wRxReferValue = update_dc_offset_on_invert(G_wRxReferValue, sample, alpha_dc);
        end
    end

    G_wRxPrevBitD = G_wRxCurrBitD;

    % ★ 상태별 처리 (기존 로직과 동일)
    switch G_wRxRunStatus
        case RX_MDM_STATUS_PREAMBLE
            % ★ 동기 패턴 검출 (Correlation 기반)
            if idx > length(G_vGmskPreamble)
                % 현재 위치에서 프리앰블 패턴과 상관관계 계산
                signal_window = BitArray(idx - length(G_vGmskPreamble) + 1 : idx);
                correlation = calculate_correlation(signal_window, G_vGmskPreamble);

                % 상관관계 히스토리 업데이트
                G_CorrelHistory = [G_CorrelHistory(2:end), correlation];

                % 신호 품질 평가
                recent_correl = G_CorrelHistory(max(1, end-10):end);
                G_SignalQuality = mean(recent_correl) / (std(recent_correl) + 0.01);

                % 동기 패턴 검출
                if (correlation > MAX_SYNC_CORRVAL && correlation > G_dMaxSyncCorrel)
                    G_dMaxSyncCorrel = correlation;
                    G_dSyncSymbolIndex = idx;
                    G_dMaxSyncCnt = 0;
                elseif (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL)
                    G_dMaxSyncCnt = G_dMaxSyncCnt + 1;
                end

                % 동기 검출 완료 조건
                if (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL && G_dMaxSyncCnt >= MAX_SYNC_COUNT)
                    % DC 오프셋 계산
                    dc_range_start = max(1, G_dSyncSymbolIndex - 50);
                    dc_range_end = G_dSyncSymbolIndex - 10;
                    if dc_range_end > dc_range_start
                        if (ENABLE_GMSK_RX_FLT > 0)
                            dc_sum = sum(G_pFilteredData(dc_range_start:dc_range_end));
                        else
                            dc_sum = sum(G_pSrcDataCh1(dc_range_start:dc_range_end));
                        end
                        G_wRxReferValue = dc_sum / (dc_range_end - dc_range_start + 1);
                    end

                    % 패킷 시작 위치 설정
                    if (idx > G_PreOffset)
                        G_PreStart = idx - G_PreOffset;
                    else
                        G_PreStart = 1;
                    end

                    % 상태 전환
                    G_dStartSymbolIndex = idx - 24;  % START_DETECT_OFFSET
                    G_dMaxSyncCorrel = 0;
                    G_dMaxSyncCnt = 0;

                    G_wRxRunStatus = RX_MDM_STATUS_DATA;  % 직접 데이터 상태로
                    G_wRxShiftReg = 0;
                    G_wRxBitCount = 0;
                    G_wRxPrevBitD = G_wRxCurrBitD;

                    % PLL 초기화
                    G_dSwRxPllValue = RX_PLL_HALF;
                    G_dSwRxPllCntrX = 1;
                    G_dSwRxPllSampC = G_wRxCurrBitD;
                    G_dSwRxPllSampP = G_wRxCurrBitD;

                    G_dSyncDetCnt = G_dSyncDetCnt + 1;
                    fprintf('✓ 동기 패턴 검출 #%d (위치: %d, 상관도: %.3f)\n', G_dSyncDetCnt, idx, G_dMaxSyncCorrel);
                end
            end

        case RX_MDM_STATUS_START
            % 시작 비트 검출 상태

        case RX_MDM_STATUS_DATA
            % 데이터 수신 상태
            if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)  % It's not a stuffing bit
                G_wRxBitCount = G_wRxBitCount + 1;

                if(G_wRxBitCount >= 300)  % 임시 최대값
                    % Stuffing bit 오류
                    if (ENABLE_PLOT98 == 1)
                        h_fig98 = figure(98);
                        h_fig98.Name = 'Stuffing Bit Error';
                        plot_range = G_PreStart:idx;
                        plot(plot_range, BitArray(plot_range), '-r'); grid;
                        title('Error Stuffing Bit');
                    end

                    % 상태 리셋
                    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                    G_wRxShiftReg  = 0;
                    G_wRxReferValue= DC_MID_LEVEL;
                    G_dStuffErrCnt = G_dStuffErrCnt + 1;
                    continue;
                end

                % 비트 데이터 추출
                G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));
                G_BitDataArray(G_wRxBitCount) = G_wNewBitData;

                % CRC 업데이트
                G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
            end

            % 종료 플래그 검출 (0x7E)
            if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                if(G_wCrcRegData == 0xf0b8)  % CRC 정상
                    % 성공 패킷 처리
                    if (ENABLE_PLOT3 == 1)
                        h_fig3 = figure(3);
                        h_fig3.Name = 'Received Data(CRC OK) - PLL+Viterbi';
                        plot_range = G_PreStart:idx+50;
                        plot(plot_range, BitArray(plot_range), '-o'); grid;
                        title('PLL + Viterbi MLSD - Received AIS Packet');
                        xlabel('Sample Index');
                        ylabel('Bit Value');
                    end

                    % 상태 리셋
                    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                    G_wRxShiftReg  = 0;
                    G_wRxReferValue= DC_MID_LEVEL;
                    G_dRcvPktCnt = G_dRcvPktCnt + 1;

                    fprintf('✓ 패킷 수신 성공 #%d (PLL + Viterbi MLSD)\n', G_dRcvPktCnt);
                else
                    % CRC 오류 처리
                    if (ENABLE_PLOT99 == 1)
                        h_fig99 = figure(99);
                        h_fig99.Name = 'Received Data(CRC ERROR) - PLL+Viterbi';
                        plot_range = G_PreStart:idx;
                        plot(plot_range, BitArray(plot_range), '-o'); grid;
                        title('PLL + Viterbi MLSD - CRC Error Packet');
                        xlabel('Sample Index');
                        ylabel('Bit Value');
                    end

                    % 상태 리셋
                    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                    G_wRxShiftReg  = 0;
                    G_wRxReferValue= DC_MID_LEVEL;
                    G_dCrcErrCnt = G_dCrcErrCnt + 1;
                    G_dCRCErrSymIdx(G_dCrcErrCnt) = G_PreStart;
                end
            end
    end
end

%------------------------------------------------------------------------
% 결과 출력
%------------------------------------------------------------------------
fprintf('\n=== AIS 수신기 성능 결과 ===\n');
fprintf('동기 검출: %d개\n', G_dSyncDetCnt);
fprintf('ADC 오류: %d개\n', G_dAdcErrCnt);
fprintf('시작 비트 오류: %d개\n', G_dStartErrCnt);
fprintf('스터핑 비트 오류: %d개\n', G_dStuffErrCnt);
fprintf('CRC 오류: %d개\n', G_dCrcErrCnt);
fprintf('성공 패킷: %d개\n', G_dRcvPktCnt);

if ENABLE_VITERBI_MLSD == 1
    fprintf('\n=== Viterbi MLSD 성능 ===\n');
    fprintf('총 비트 결정: %d개\n', G_ViterbiStats.total_decisions);
    fprintf('Viterbi 고신뢰도 결정: %d개\n', G_ViterbiStats.viterbi_decisions);
    if G_ViterbiStats.total_decisions > 0
        avg_confidence = G_ViterbiStats.confidence_sum / G_ViterbiStats.total_decisions;
        viterbi_ratio = (G_ViterbiStats.viterbi_decisions / G_ViterbiStats.total_decisions) * 100;
        fprintf('평균 신뢰도: %.3f\n', avg_confidence);
        fprintf('Viterbi 활용률: %.1f%%\n', viterbi_ratio);
    end
end

fprintf('\n=== 처리 완료 ===\n');

%------------------------------------------------------------------------
% Viterbi MLSD 비트 결정 함수 (핵심)
%------------------------------------------------------------------------
function [bit_decision, confidence] = viterbi_mlsd_bit_decision(pll_sample, current_idx)
    global G_ViterbiBuffer G_ViterbiIndex VITERBI_WINDOW_SIZE;
    global G_pFilteredData ENABLE_GMSK_RX_FLT G_pSrcDataCh1;

    % 기본값 (PLL 결과)
    bit_decision = pll_sample;
    confidence = 0.5;

    % Viterbi 버퍼에 현재 샘플 추가
    G_ViterbiBuffer(G_ViterbiIndex) = pll_sample;
    G_ViterbiIndex = mod(G_ViterbiIndex, VITERBI_WINDOW_SIZE) + 1;

    % 충분한 히스토리가 있을 때만 Viterbi 적용
    if current_idx < VITERBI_WINDOW_SIZE
        return;
    end

    % 주변 신호 샘플 추출 (over sampling 고려)
    signal_start = max(1, current_idx - VITERBI_WINDOW_SIZE * 5);
    signal_end = min(length(G_pFilteredData), current_idx + VITERBI_WINDOW_SIZE * 5);

    if signal_end <= signal_start
        return;
    end

    if (ENABLE_GMSK_RX_FLT > 0)
        signal_window = G_pFilteredData(signal_start:signal_end);
    else
        signal_window = G_pSrcDataCh1(signal_start:signal_end);
    end

    % 2-상태 Viterbi MLSD
    [viterbi_bit, confidence] = simple_viterbi_mlsd(signal_window, G_ViterbiBuffer);

    % 신뢰도가 높으면 Viterbi 결과 사용
    if confidence > 0.4
        bit_decision = viterbi_bit;
    end
end

%------------------------------------------------------------------------
% 간단한 Viterbi MLSD 구현
%------------------------------------------------------------------------
function [bit_decision, confidence] = simple_viterbi_mlsd(signal_window, bit_history)
    % 기본값
    if ~isempty(bit_history)
        bit_decision = bit_history(length(bit_history));
    else
        bit_decision = 0;
    end
    confidence = 0.3;

    if length(signal_window) < 10 || length(bit_history) < 4
        return;
    end

    % 신호 정규화
    signal_mean = mean(signal_window);
    signal_std = std(signal_window);
    if signal_std > 0.01
        normalized_signal = (signal_window - signal_mean) / signal_std;
    else
        normalized_signal = signal_window - signal_mean;
    end

    % 2-상태 Viterbi (간단한 버전)
    num_states = 2;
    window_length = min(length(normalized_signal), 20);  % 최대 20 샘플

    % 경로 메트릭 초기화
    path_metrics = [0, inf];

    % Viterbi 알고리즘
    for t = 1:window_length
        new_metrics = [inf, inf];
        current_sample = normalized_signal(t);

        for prev_state = 1:num_states
            if path_metrics(prev_state) < inf
                for output_bit = 0:1
                    % 예상 신호 레벨
                    if output_bit == 0
                        expected_level = -0.5;
                        next_state = 1;
                    else
                        expected_level = 0.5;
                        next_state = 2;
                    end

                    % 브랜치 메트릭
                    error = current_sample - expected_level;
                    branch_metric = error^2;
                    total_metric = path_metrics(prev_state) + branch_metric;

                    if total_metric < new_metrics(next_state)
                        new_metrics(next_state) = total_metric;
                        if t == window_length
                            bit_decision = output_bit;
                        end
                    end
                end
            end
        end

        path_metrics = new_metrics;
    end

    % 신뢰도 계산
    [min_metric, ~] = min(path_metrics);
    if min_metric < inf
        confidence = exp(-min_metric / window_length);
        confidence = max(0.1, min(0.9, confidence));
    end

    % 비트 히스토리와의 일관성 체크
    if length(bit_history) >= 3
        hist_len = length(bit_history);
        recent_pattern = bit_history(hist_len-2:hist_len);
        if sum(recent_pattern == bit_decision) >= 2
            confidence = confidence * 1.2;  % 일관성 보너스
        end
    end

    confidence = min(0.9, confidence);
end

%------------------------------------------------------------------------
% 기존 함수들 (NRZI 디코딩, DC 오프셋 등)
%------------------------------------------------------------------------
function decoded_bit = nrzi_decode(signal_value, reference_value)
    if signal_value > reference_value
        decoded_bit = 1;
    else
        decoded_bit = 0;
    end
end

function new_dc_offset = update_dc_offset_on_invert(current_dc, sample, alpha)
    new_dc_offset = alpha * current_dc + (1 - alpha) * sample;
end

%------------------------------------------------------------------------
% CRC 업데이트 함수 (기존과 동일)
%------------------------------------------------------------------------
function crc_out = update_crc(crc_in, data_bit)
    % AIS CRC-16 계산
    crc_out = crc_in;

    if bitand(bitxor(bitshift(crc_out, -15), data_bit), 0x0001)
        crc_out = bitxor(bitshift(bitand(crc_out, 0x7fff), 1), 0x1021);
    else
        crc_out = bitshift(bitand(crc_out, 0x7fff), 1);
    end

    crc_out = bitand(crc_out, 0xffff);
end
