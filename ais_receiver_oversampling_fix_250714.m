clear;

%------------------------------------------------------------------------
% AIS Receiver with Over Sampling Rate 5 Fix
% 기존 성공한 로직 + Over sampling rate 5 고려
% Based on: ais_receiver_collation_with_real_data_250712.m
% Created: 2024-07-14
%------------------------------------------------------------------------

%-------------------------------------------------------------------------
% 설정 파라미터
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data
ENABLE_NOTCH_FLT        = 1;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 1;                % 0: Disable, 1: Adaptive DC Offset
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable

% ★ Over Sampling 관련 설정
OVERSAMPLING_RATE       = 5;                % Over sampling rate (중요!)
SYMBOL_CENTER_OFFSET    = 2;                % 심벌 중심점 오프셋 (0~4 중 2번째)
ENABLE_SYMBOL_TIMING    = 1;                % 0: 기존방식, 1: 심벌 타이밍 적용
ENABLE_ADAPTIVE_TIMING  = 1;                % 0: 고정 오프셋, 1: 적응적 타이밍

%-------------------------------------------------------------------------
ENABLE_DEBUG            = 0;
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Received Data packet with CRC
else
    ENABLE_PLOT1        = 0;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 0;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 성공 패킷은 항상 표시
end

ENABLE_DEBUG_ERROR      = 0;
if (ENABLE_DEBUG_ERROR == 1)
    ENABLE_PLOT96       = 1;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 1;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 1;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % 0: Disable, 1: CRC Error
else
    ENABLE_PLOT96       = 0;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 0;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 0;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % CRC 오류는 항상 표시
end

%-------------------------------------------------------------------------
% 기존 파라미터 유지
%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = .750;
MAX_SYNC_COUNT          = 25;

%-------------------------------------------------------------------------
% 상수 정의 (기존과 동일)
%-------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_PRELOAD   = 1;
RX_MDM_STATUS_DATA      = 2;

SYNC_DETECT_OFFSET      = 24;
DC_MID_LEVEL           = 0;

%-------------------------------------------------------------------------
% 전역 변수 초기화 (기존과 동일)
%-------------------------------------------------------------------------
G_dSyncDetCnt    = 0;
G_dAdcErrCnt     = 0;
G_dStartErrCnt   = 0;
G_dStuffErrCnt   = 0;
G_dCrcErrCnt     = 0;
G_dRcvPktCnt     = 0;

G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
G_wRxShiftReg    = 0;
G_wRxBitCount    = 0;
G_bRxByteData    = 0;
G_wCrcRegData    = 0xffff;
G_wRxReferValue  = DC_MID_LEVEL;
G_PreStart       = 1;
G_PreOffset      = 100;

% 데이터 배열
G_BitDataArray   = zeros(1, 1000);
G_dCRCErrSymIdx  = zeros(1, 1000);

% ★ Over Sampling 통계
G_OverSamplingStats = struct();
G_OverSamplingStats.total_symbols = 0;
G_OverSamplingStats.processed_samples = 0;
G_OverSamplingStats.symbol_timing_adjustments = 0;

%------------------------------------------------------------------------
% 데이터 로드 (기존과 동일)
%------------------------------------------------------------------------
fprintf('AIS 데이터 로딩 중...\n');

% 데이터 파일 읽기
fid = fopen('DumpData/DUMPDATA_250525_ch2.bin', 'rb');
if fid == -1
    error('데이터 파일을 열 수 없습니다: DumpData/DUMPDATA_250525_ch2.bin');
end

G_pSrcDataCh1 = fread(fid, inf, 'int16');
fclose(fid);

if isempty(G_pSrcDataCh1)
    error('데이터 파일이 비어있습니다.');
end

fprintf('데이터 로드 완료: %d 샘플\n', length(G_pSrcDataCh1));

% 데이터 정규화
G_pSrcDataCh1 = double(G_pSrcDataCh1) / 32768.0;

%------------------------------------------------------------------------
% 필터 초기화 (기존과 동일)
%------------------------------------------------------------------------
if (ENABLE_GMSK_RX_FLT > 0)
    % GMSK 수신 필터 설정
    filter_taps = 64;
    G_pFilteredData = filter(ones(1, filter_taps)/filter_taps, 1, G_pSrcDataCh1);
    fprintf('GMSK 수신 필터 적용 완료\n');
else
    G_pFilteredData = G_pSrcDataCh1;
end

%------------------------------------------------------------------------
% 메인 처리 루프 (Over Sampling Rate 5 적용)
%------------------------------------------------------------------------
fprintf('\n=== Over Sampling Rate 5 AIS 수신기 시작 ===\n');
fprintf('전체 샘플 수: %d\n', length(G_pSrcDataCh1));
fprintf('예상 심벌 수: %d\n', floor(length(G_pSrcDataCh1) / OVERSAMPLING_RATE));

nDataLength = length(G_pSrcDataCh1);
nSymbolLength = floor(nDataLength / OVERSAMPLING_RATE);

% 동적 DC 오프셋 계산용
AdativeDcOffset = zeros(1, nSymbolLength);
BitArray = zeros(1, nSymbolLength);

% ★ 핵심: Over Sampling Rate 5를 고려한 심벌 추출
symbol_idx = 1;
for sample_idx = 1:OVERSAMPLING_RATE:nDataLength
    if mod(sample_idx, 50000) == 0
        fprintf('처리 진행률: %.1f%% (심벌: %d/%d)\n', ...
            (sample_idx/nDataLength)*100, symbol_idx, nSymbolLength);
    end
    
    % ★ 적응적 심벌 중심점 찾기
    if ENABLE_ADAPTIVE_TIMING == 1 && sample_idx + 4 <= nDataLength
        % 5개 샘플 중 최적 위치 찾기 (신호 강도 기반)
        signal_strengths = zeros(1, 5);
        for offset = 0:4
            if sample_idx + offset <= nDataLength
                if (ENABLE_GMSK_RX_FLT > 0)
                    signal_strengths(offset+1) = abs(G_pFilteredData(sample_idx + offset));
                else
                    signal_strengths(offset+1) = abs(G_pSrcDataCh1(sample_idx + offset));
                end
            end
        end

        % 최대 신호 강도 위치를 중심점으로 선택
        [~, best_offset] = max(signal_strengths);
        center_sample_idx = sample_idx + best_offset - 1;

        % 통계 업데이트
        if best_offset - 1 ~= SYMBOL_CENTER_OFFSET
            G_OverSamplingStats.symbol_timing_adjustments = G_OverSamplingStats.symbol_timing_adjustments + 1;
        end
    else
        % 고정 오프셋 사용
        center_sample_idx = sample_idx + SYMBOL_CENTER_OFFSET;
    end

    if center_sample_idx > nDataLength
        break;
    end

    % 현재 심벌 신호값
    if (ENABLE_GMSK_RX_FLT > 0)
        current_signal = G_pFilteredData(center_sample_idx);
    else
        current_signal = G_pSrcDataCh1(center_sample_idx);
    end
    
    % 동적 DC 오프셋 계산 (심벌 단위)
    if symbol_idx > 20
        start_symbol = max(1, symbol_idx - 19);
        start_sample = (start_symbol - 1) * OVERSAMPLING_RATE + 1;
        end_sample = min(center_sample_idx, nDataLength);
        AdativeDcOffset(symbol_idx) = mean(G_pFilteredData(start_sample:end_sample));
    else
        AdativeDcOffset(symbol_idx) = mean(G_pFilteredData(1:center_sample_idx));
    end
    
    % 비트 결정 (기존 방식)
    if current_signal > AdativeDcOffset(symbol_idx)
        BitArray(symbol_idx) = 1;
    else
        BitArray(symbol_idx) = 0;
    end
    
    % 통계 업데이트
    G_OverSamplingStats.total_symbols = G_OverSamplingStats.total_symbols + 1;
    G_OverSamplingStats.processed_samples = sample_idx;
    
    symbol_idx = symbol_idx + 1;
    
    % 배열 크기 체크
    if symbol_idx > nSymbolLength
        break;
    end
end

fprintf('심벌 추출 완료: %d개 심벌 처리\n', G_OverSamplingStats.total_symbols);

%------------------------------------------------------------------------
% 심벌 기반 AIS 패킷 처리 (기존 로직 적용)
%------------------------------------------------------------------------
fprintf('\n=== 심벌 기반 AIS 패킷 처리 시작 ===\n');

nCurSymbolIdx = 1;
for idx = 1:G_OverSamplingStats.total_symbols
    if mod(idx, 10000) == 0
        fprintf('패킷 처리 진행률: %.1f%%\n', (idx/G_OverSamplingStats.total_symbols)*100);
    end

    % 상태별 처리 (기존 로직과 동일)
    switch G_wRxRunStatus
        case RX_MDM_STATUS_PREAMBLE
            % 동기 패턴 검출 (심벌 기반)
            [sync_detected, nCurSymbolIdx] = detect_sync_pattern_symbol(idx, BitArray);

            if sync_detected
                G_dSyncDetCnt = G_dSyncDetCnt + 1;

                % 패킷 시작 위치 설정
                if (nCurSymbolIdx > G_PreOffset)
                    G_PreStart = nCurSymbolIdx - G_PreOffset;
                else
                    G_PreStart = 1;
                end

                fprintf('동기 패턴 검출 #%d (심벌 위치: %d)\n', G_dSyncDetCnt, nCurSymbolIdx);

                % 디버깅: 동기 패턴 주변 비트 출력
                if nCurSymbolIdx >= 8 && nCurSymbolIdx <= length(BitArray) - 20
                    sync_bits = BitArray(nCurSymbolIdx-7:nCurSymbolIdx+12);
                    fprintf('  동기 주변 비트: ');
                    for b = 1:length(sync_bits)
                        fprintf('%d', sync_bits(b));
                    end
                    fprintf('\n');
                end

                % 데이터 수신 상태로 전환
                G_wRxRunStatus = RX_MDM_STATUS_DATA;
                G_wRxBitCount = 0;
                G_bRxByteData = 0;
                G_wCrcRegData = 0xffff;
            end

        case RX_MDM_STATUS_DATA
            % 데이터 수신 처리 (심벌 기반)
            process_symbol_data(idx, BitArray, AdativeDcOffset);
    end
end

%------------------------------------------------------------------------
% 결과 출력
%------------------------------------------------------------------------
fprintf('\n=== AIS 수신기 성능 결과 ===\n');
fprintf('동기 검출: %d개\n', G_dSyncDetCnt);
fprintf('ADC 오류: %d개\n', G_dAdcErrCnt);
fprintf('시작 비트 오류: %d개\n', G_dStartErrCnt);
fprintf('스터핑 비트 오류: %d개\n', G_dStuffErrCnt);
fprintf('CRC 오류: %d개\n', G_dCrcErrCnt);
fprintf('성공 패킷: %d개\n', G_dRcvPktCnt);

fprintf('\n=== Over Sampling 성능 ===\n');
fprintf('처리된 샘플: %d개\n', G_OverSamplingStats.processed_samples);
fprintf('추출된 심벌: %d개\n', G_OverSamplingStats.total_symbols);
fprintf('샘플/심벌 비율: %.2f\n', G_OverSamplingStats.processed_samples / G_OverSamplingStats.total_symbols);

%------------------------------------------------------------------------
% 성능 그래프
%------------------------------------------------------------------------
figure(9);
bar_x = ["SyncDet" "AdcErr" "StartErr" "StuffErr" "CrcErr" "Packet OK"];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y, 'FaceColor', 'flat');
b.CData(6,:) = [0.0 0.8 0.0];  % 성공 패킷 (녹색)
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')
title('AIS Receiver with Over Sampling Rate 5 Fix');

fprintf('\n=== 처리 완료 ===\n');

%------------------------------------------------------------------------
% 심벌 기반 동기 패턴 검출
%------------------------------------------------------------------------
function [sync_detected, sync_position] = detect_sync_pattern_symbol(current_symbol_idx, bit_array)
    sync_detected = false;
    sync_position = current_symbol_idx;

    % AIS 동기 패턴: 01111110 (0x7E)
    sync_pattern = [0 1 1 1 1 1 1 0];
    pattern_length = length(sync_pattern);

    if current_symbol_idx >= pattern_length
        % 현재 위치에서 패턴 매칭
        current_pattern = bit_array(current_symbol_idx-pattern_length+1:current_symbol_idx);

        % 패턴 일치도 계산
        match_count = sum(current_pattern == sync_pattern);
        match_ratio = match_count / pattern_length;

        % 87.5% 이상 일치하면 동기 패턴으로 인식 (7/8 = 0.875)
        if match_ratio >= 0.875
            sync_detected = true;
            sync_position = current_symbol_idx;
        end
    end
end

%------------------------------------------------------------------------
% 심벌 기반 데이터 처리
%------------------------------------------------------------------------
function process_symbol_data(current_symbol_idx, bit_array, dc_offset_array)
    global G_wRxRunStatus G_wRxShiftReg G_wRxBitCount G_bRxByteData G_wCrcRegData;
    global G_wNewBitData G_BitDataArray G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt;
    global G_PreStart G_dCRCErrSymIdx ENABLE_PLOT3 ENABLE_PLOT99;
    global RX_MDM_STATUS_PREAMBLE DC_MID_LEVEL;

    if current_symbol_idx > length(bit_array)
        return;
    end

    current_bit = bit_array(current_symbol_idx);

    % 데이터 타입 변환 (비트 연산용)
    current_bit = uint16(current_bit);

    % 시프트 레지스터 업데이트 (기존과 동일)
    G_wRxShiftReg = bitor(bitshift(bitand(uint16(G_wRxShiftReg), uint16(0x00ff)), 1), current_bit);

    % 디버깅: 처음 몇 비트 출력
    if G_wRxBitCount < 10
        fprintf('  비트 #%d: %d, 시프트레지스터: 0x%04x\n', G_wRxBitCount, current_bit, G_wRxShiftReg);
    end

    % Stuff bit 검출 및 처리 (기존과 동일)
    if (bitand(uint16(G_wRxShiftReg), uint16(0x3f00)) ~= uint16(0x3e00))      % It's not a stuffing bit
        G_wRxBitCount = G_wRxBitCount + 1;

        % 최대 비트 수 체크
        if(G_wRxBitCount >= 300)  % 임시 최대값
            % Stuffing bit 오류
            if (ENABLE_PLOT99 == 1)
                h_fig99 = figure(99);
                h_fig99.Name = 'Stuffing Bit Error (Symbol Based)';
                symbol_range = G_PreStart:current_symbol_idx;
                plot(symbol_range, bit_array(symbol_range), '-r'); grid;
                title('Error Stuffing Bit (Symbol Based)');
            end

            % 상태 리셋
            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
            G_wRxShiftReg  = 0;
            G_bRxByteData = 0;
            G_dStuffErrCnt = G_dStuffErrCnt + 1;
            return;
        end

        % 비트 데이터 추출 (기존과 동일)
        G_wNewBitData = bitand(bitshift(uint16(G_wRxShiftReg), -8), uint16(0x0001));
        G_bRxByteData = bitor(bitshift(uint16(G_bRxByteData), -1), bitand(bitshift(uint16(G_wRxShiftReg), -1), uint16(0x0080)));
        G_BitDataArray(G_wRxBitCount) = G_wNewBitData;

        % CRC 업데이트 (기존과 동일)
        G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
    end

    % 종료 플래그 검출 (0x7E)
    if (bitand(uint16(G_wRxShiftReg), uint16(0x00ff)) == uint16(0x007e))
        if(G_wCrcRegData == 0xf0b8)  % CRC 정상
            % 성공 패킷 처리
            if (ENABLE_PLOT3 == 1)
                h_fig3 = figure(3);
                h_fig3.Name = 'Received Data(CRC OK) - Symbol Based';
                symbol_range = G_PreStart:current_symbol_idx+10;
                if length(symbol_range) <= length(bit_array)
                    plot(symbol_range, bit_array(symbol_range), '-o'); grid;
                    title('Over Sampling Rate 5 - Received AIS Packet');
                    xlabel('Symbol Index');
                    ylabel('Bit Value');
                end
            end

            % 상태 리셋
            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
            G_wRxShiftReg  = 0;
            G_bRxByteData = 0;
            G_dRcvPktCnt = G_dRcvPktCnt + 1;

            fprintf('✓ 패킷 수신 성공 #%d (Over Sampling Rate 5)\n', G_dRcvPktCnt);
        else
            % CRC 오류 처리
            if (ENABLE_PLOT99 == 1)
                h_fig99 = figure(99);
                h_fig99.Name = 'Received Data(CRC ERROR) - Symbol Based';
                symbol_range = G_PreStart:current_symbol_idx;
                if length(symbol_range) <= length(bit_array)
                    plot(symbol_range, bit_array(symbol_range), '-o'); grid;
                    title('Over Sampling Rate 5 - CRC Error Packet');
                    xlabel('Symbol Index');
                    ylabel('Bit Value');
                end
            end

            % 상태 리셋
            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
            G_wRxShiftReg  = 0;
            G_bRxByteData = 0;
            G_dCrcErrCnt = G_dCrcErrCnt + 1;
            G_dCRCErrSymIdx(G_dCrcErrCnt) = G_PreStart;
        end
    end
end

%------------------------------------------------------------------------
% CRC 업데이트 함수 (기존과 동일)
%------------------------------------------------------------------------
function crc_out = update_crc(crc_in, data_bit)
    % AIS CRC-16 계산 (데이터 타입 변환)
    crc_out = uint16(crc_in);
    data_bit = uint16(data_bit);

    if bitand(bitxor(bitshift(crc_out, -15), data_bit), uint16(0x0001))
        crc_out = bitxor(bitshift(bitand(crc_out, uint16(0x7fff)), 1), uint16(0x1021));
    else
        crc_out = bitshift(bitand(crc_out, uint16(0x7fff)), 1);
    end

    crc_out = bitand(crc_out, uint16(0xffff));
end
