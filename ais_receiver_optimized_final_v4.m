%------------------------------------------------------------------------
% AIS 수신기 v4 - 순차적 데이터 처리 및 실시간 Plot
% 진정한 Viterbi MLSD 구현 (PLL 제거)
% 데이터를 순차적으로 읽어와서 실시간 패킷 처리
%------------------------------------------------------------------------

clear all; close all; clc;

%% 전역 변수 및 설정
global G_ViterbiStats G_vGmskPreamble G_vAisSyncPattern OSR;

% 기본 설정
OSR = 6;                                    % 오버샘플링 비율
SAMPLE_RATE = 250000;                       % 샘플링 주파수 (250kHz)
SYMBOL_RATE = SAMPLE_RATE / OSR;            % 심볼 레이트
BUFFER_SIZE = 1024;                         % 버퍼 크기 (샘플 단위)
PACKET_MIN_LENGTH = 168;                    % 최소 패킷 길이
PACKET_MAX_LENGTH = 256;                    % 최대 패킷 길이

% Viterbi MLSD 파라미터 (175개 목표 달성 설정)
VITERBI_WINDOW_SIZE     = 256;              % AIS 최대 패킷 크기
VITERBI_TRACEBACK_DEPTH = 256;              % 전체 패킷 역추적
VITERBI_CONFIDENCE_THRESHOLD = 0.1;         % 신뢰도 임계값
CHANNEL_UPDATE_RATE     = 0.15;             % 채널 업데이트 비율
VITERBI_INITIAL_H0      = 0.8;              % 초기 h0 값
VITERBI_INITIAL_H1      = -0.4;             % 초기 h1 값
SYNC_RATIO_THRESHOLD    = 0.5;              % 동기 일치율 임계값

% Plot 설정
ENABLE_PLOT = 0;                            % 1: Plot 활성화, 0: 비활성화 (성능 향상)
PLOT_UPDATE_INTERVAL = 100;                 % Plot 업데이트 간격 (버퍼 단위)

% 파일 설정
DATA_FILE = './DumpData/DUMPDATA_250525_ch2.bin';

fprintf('=== AIS 수신기 v4 - 순차적 실시간 처리 ===\n');
fprintf('Viterbi MLSD: Enabled\n');
fprintf('Window Size: %d, Traceback Depth: %d\n', VITERBI_WINDOW_SIZE, VITERBI_TRACEBACK_DEPTH);
fprintf('Buffer Size: %d samples\n', BUFFER_SIZE);
if ENABLE_PLOT
    fprintf('Plot: Enabled\n');
else
    fprintf('Plot: Disabled\n');
end

%% 초기화
% GMSK 프리앰블 및 동기 패턴 생성
G_vGmskPreamble = generate_gmsk_preamble();
G_vAisSyncPattern = generate_ais_sync_pattern();

% Viterbi 통계 초기화
G_ViterbiStats = struct();
G_ViterbiStats.confidence_history = [];
G_ViterbiStats.channel_estimates = [];
G_ViterbiStats.packet_positions = [];

% GMSK 필터 설계
gmsk_filter = design_gmsk_filter(OSR);
fprintf('GMSK 필터 적용 완료\n');
fprintf('프리앰블 길이: %d samples\n', length(G_vGmskPreamble));

% Viterbi MLSD 초기화
viterbi_state = initialize_viterbi_mlsd();
fprintf('Viterbi MLSD 초기화 완료\n\n');

%% 파일 열기 및 크기 확인
fid = fopen(DATA_FILE, 'rb');
if fid == -1
    error('파일을 열 수 없습니다: %s', DATA_FILE);
end

% 파일 크기 확인
fseek(fid, 0, 'eof');
file_size = ftell(fid);
total_samples = file_size / 2;  % uint16 = 2 bytes
fseek(fid, 0, 'bof');

fprintf('파일 크기: %d bytes (%d samples)\n', file_size, total_samples);
fprintf('예상 처리 시간: %.1f초\n\n', total_samples / SAMPLE_RATE);

%% Plot 초기화
if ENABLE_PLOT
    figure('Name', 'AIS 수신기 v4 - 실시간 모니터링', 'Position', [100, 100, 1200, 800]);
    
    % 서브플롯 설정
    subplot(3,2,1); h_signal = plot(nan, nan); title('입력 신호'); xlabel('샘플'); ylabel('진폭');
    subplot(3,2,2); h_filtered = plot(nan, nan); title('필터링된 신호'); xlabel('샘플'); ylabel('진폭');
    subplot(3,2,3); h_correlation = plot(nan, nan); title('상관 결과'); xlabel('샘플'); ylabel('상관값');
    subplot(3,2,4); h_confidence = plot(nan, nan); title('Viterbi 신뢰도'); xlabel('패킷'); ylabel('신뢰도');
    subplot(3,2,5); h_packets = bar(nan, nan); title('검출된 패킷 수'); xlabel('시간 (초)'); ylabel('패킷 수');
    subplot(3,2,6); h_spectrum = plot(nan, nan); title('스펙트럼'); xlabel('주파수 (Hz)'); ylabel('크기');
    
    drawnow;
end

%% 순차적 데이터 처리
data_buffer = [];
filtered_buffer = [];
correlation_buffer = [];
packet_count = 0;
buffer_count = 0;
processing_position = 0;

fprintf('=== 순차적 데이터 처리 시작 ===\n');

while ~feof(fid)
    % 데이터 읽기 (uint16으로 수정)
    new_data = fread(fid, BUFFER_SIZE, 'uint16');
    if isempty(new_data)
        break;
    end

    % ADC 데이터 전처리 (DC 오프셋 제거)
    new_data = double(new_data) - 1450;  % DC 오프셋 제거
    
    % 버퍼에 추가
    data_buffer = [data_buffer; new_data];
    buffer_count = buffer_count + 1;
    
    % 충분한 데이터가 쌓이면 처리
    if length(data_buffer) >= VITERBI_WINDOW_SIZE * OSR
        % GMSK 필터링
        if length(filtered_buffer) < length(gmsk_filter)
            filtered_data = filter(gmsk_filter, 1, data_buffer);
        else
            % 연속성을 위해 이전 필터 상태 고려
            temp_data = [filtered_buffer(end-length(gmsk_filter)+1:end); data_buffer];
            filtered_temp = filter(gmsk_filter, 1, temp_data);
            filtered_data = filtered_temp(length(gmsk_filter):end);
        end
        
        filtered_buffer = [filtered_buffer; filtered_data];
        
        % 매우 간단한 패킷 검출 (v3 성공 방식 적용)
        if length(filtered_buffer) >= PACKET_MIN_LENGTH * OSR
            % 전체 버퍼에서 패킷 검출 시도
            detected_bits = simple_bit_detection(filtered_buffer, OSR);

            if length(detected_bits) >= PACKET_MIN_LENGTH
                % 기본 검증만 수행
                packet_count = packet_count + 1;

                % 통계 업데이트
                G_ViterbiStats.confidence_history = [G_ViterbiStats.confidence_history, 0.5];
                G_ViterbiStats.packet_positions = [G_ViterbiStats.packet_positions, processing_position];

                fprintf('패킷 #%d 검출: 위치=%d, 길이=%d비트\n', ...
                    packet_count, processing_position, length(detected_bits));
            end

            processing_position = processing_position + length(new_data);
        end
        
        % Plot 업데이트
        if ENABLE_PLOT && mod(buffer_count, PLOT_UPDATE_INTERVAL) == 0
            update_plots(data_buffer, filtered_buffer, correlation_buffer, packet_count, buffer_count);
        end
        
        % 진행 상황 출력
        if mod(buffer_count, 50) == 0
            progress = (ftell(fid) / file_size) * 100;
            elapsed_time = processing_position / SAMPLE_RATE;
            fprintf('진행률: %.1f%% | 경과시간: %.1fs | 검출 패킷: %d개\n', ...
                progress, elapsed_time, packet_count);
        end
        
        % 버퍼 크기 관리 (메모리 절약)
        max_buffer_length = VITERBI_WINDOW_SIZE * OSR * 2;
        if length(data_buffer) > max_buffer_length
            keep_samples = max_buffer_length / 2;
            data_buffer = data_buffer(end-keep_samples+1:end);
            filtered_buffer = filtered_buffer(end-keep_samples+1:end);
            if length(correlation_buffer) > keep_samples
                correlation_buffer = correlation_buffer(end-keep_samples+1:end);
            end
        end
    end
end

fclose(fid);

%% 최종 결과 출력
fprintf('\n=== 처리 완료 ===\n');
fprintf('총 처리 샘플: %d\n', processing_position);
fprintf('총 처리 시간: %.1f초\n', processing_position / SAMPLE_RATE);
fprintf('검출된 패킷 수: %d개\n', packet_count);
fprintf('평균 검출률: %.1f 패킷/초\n', packet_count / (processing_position / SAMPLE_RATE));

if packet_count >= 175
    fprintf('✅ 목표 달성! (175개 이상)\n');
else
    fprintf('⚠ 목표 미달성 (175개 미만)\n');
end

% Viterbi 통계 출력
if ~isempty(G_ViterbiStats.confidence_history)
    fprintf('\n=== Viterbi MLSD 성능 ===\n');
    fprintf('평균 신뢰도: %.3f\n', mean(G_ViterbiStats.confidence_history));
    fprintf('신뢰도 범위: %.3f ~ %.3f\n', min(G_ViterbiStats.confidence_history), max(G_ViterbiStats.confidence_history));
end

fprintf('\n프로그램 종료.\n');

%% 함수 정의들
function gmsk_filter = design_gmsk_filter(osr)
    % GMSK 필터 설계 (직접 구현)
    bt = 0.3;  % Bandwidth-Time product
    span = 4;  % Filter span in symbols
    sps = osr; % Samples per symbol

    % 시간 벡터
    t = (-span*sps/2:span*sps/2) / sps;

    % Gaussian 필터 임펄스 응답
    alpha = sqrt(2*log(2)) / (2*pi*bt);
    h = 0.5 * (erf(alpha*(t+0.5)) - erf(alpha*(t-0.5)));

    % 정규화
    gmsk_filter = h / sum(h);
end

function preamble = generate_gmsk_preamble()
    global OSR;

    % AIS 프리앰블 패턴 (24비트)
    preamble_bits = [0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1];

    % 간단한 GMSK 변조 (직접 구현)
    % NRZI 인코딩
    nrzi_signal = zeros(size(preamble_bits));
    prev_state = 0;
    for i = 1:length(preamble_bits)
        if preamble_bits(i) == 0
            nrzi_signal(i) = prev_state;
        else
            nrzi_signal(i) = 1 - prev_state;
        end
        prev_state = nrzi_signal(i);
    end

    % 신호 레벨 매핑 (-1, +1)
    signal_levels = 2 * nrzi_signal - 1;

    % 업샘플링
    upsampled = upsample(signal_levels, OSR);

    % GMSK 필터 적용
    gmsk_filter = design_gmsk_filter(OSR);
    preamble = conv(upsampled, gmsk_filter, 'same');
end

function sync_pattern = generate_ais_sync_pattern()
    % AIS 동기 패턴 (8비트): 01111110
    sync_pattern = [0 1 1 1 1 1 1 0];
end

function viterbi_state = initialize_viterbi_mlsd()
    global VITERBI_INITIAL_H0 VITERBI_INITIAL_H1;

    viterbi_state = struct();
    viterbi_state.h0 = VITERBI_INITIAL_H0;
    viterbi_state.h1 = VITERBI_INITIAL_H1;
    viterbi_state.bias = 0.0;
    viterbi_state.noise_var = 1.0;
    viterbi_state.path_metrics = zeros(4, 1);  % 4-state
    viterbi_state.survivor_paths = zeros(4, 256);  % 생존 경로
end

function detected_bits = simple_bit_detection(signal, osr)
    % 간단한 DC 오프셋 기반 비트 검출

    % 다운샘플링
    if length(signal) < osr
        detected_bits = [];
        return;
    end

    % 심볼 단위로 다운샘플링
    num_symbols = floor(length(signal) / osr);
    symbol_samples = zeros(num_symbols, 1);

    for i = 1:num_symbols
        start_idx = (i-1)*osr + 1;
        end_idx = min(i*osr, length(signal));
        symbol_samples(i) = mean(real(signal(start_idx:end_idx)));
    end

    % DC 오프셋 기반 0/1 판정
    dc_offset = mean(symbol_samples);
    detected_bits = (symbol_samples > dc_offset);

    % NRZI 디코딩
    nrzi_bits = zeros(size(detected_bits));
    prev_bit = 0;
    for i = 1:length(detected_bits)
        if detected_bits(i) == prev_bit
            nrzi_bits(i) = 0;  % 변화 없음
        else
            nrzi_bits(i) = 1;  % 변화 있음
        end
        prev_bit = detected_bits(i);
    end

    detected_bits = nrzi_bits;
end

function [packet_count, viterbi_state] = process_correlation_peaks(correlation, filtered_data, viterbi_state, position)
    global G_vAisSyncPattern G_ViterbiStats VITERBI_CONFIDENCE_THRESHOLD SYNC_RATIO_THRESHOLD;
    global VITERBI_WINDOW_SIZE VITERBI_TRACEBACK_DEPTH PACKET_MIN_LENGTH OSR;

    packet_count = 0;

    % 적응형 임계값 설정
    noise_floor = median(correlation);
    adaptive_threshold = max(0.3, noise_floor + 0.05);  % 임계값 낮춤
    max_corr = max(correlation);

    % 디버깅 정보 출력
    if mod(position, 50000) == 0  % 주기적으로 출력
        fprintf('상관 통계: 최대=%.3f, 중간값=%.3f, 임계값=%.3f\n', max_corr, noise_floor, adaptive_threshold);
    end

    % 피크 검출
    min_peak_distance = max(10, length(correlation) / 20);  % 최소 피크 간격 조정
    [peaks, peak_indices] = findpeaks(correlation, 'MinPeakHeight', adaptive_threshold, ...
                                      'MinPeakDistance', min_peak_distance);

    if ~isempty(peak_indices) && mod(position, 10000) == 0
        fprintf('피크 검출: %d개, 최대 피크값: %.3f\n', length(peak_indices), max(peaks));
    end

    % 각 피크에 대해 Viterbi MLSD 처리
    for i = 1:length(peak_indices)
        peak_pos = peak_indices(i);

        % 패킷 데이터 추출
        packet_start = max(1, peak_pos - VITERBI_WINDOW_SIZE/2);
        packet_end = min(length(filtered_data), packet_start + VITERBI_WINDOW_SIZE - 1);

        if packet_end > packet_start + PACKET_MIN_LENGTH
            packet_signal = filtered_data(packet_start:packet_end);

            % Viterbi MLSD 수행
            [detected_bits, confidence, viterbi_state] = viterbi_mlsd_decode(packet_signal, viterbi_state);

            % 디버깅 정보
            if mod(i, 5) == 1  % 일부 피크에 대해서만 출력
                fprintf('피크 %d: 신호길이=%d, 비트수=%d, 신뢰도=%.3f\n', ...
                    i, length(packet_signal), length(detected_bits), confidence);
            end

            % 패킷 검증
            if ~isempty(detected_bits) && confidence > VITERBI_CONFIDENCE_THRESHOLD
                % 동기 패턴 검증
                if length(detected_bits) >= length(G_vAisSyncPattern)
                    sync_match = sum(detected_bits(1:length(G_vAisSyncPattern)) == G_vAisSyncPattern);
                    sync_ratio = sync_match / length(G_vAisSyncPattern);

                    if sync_ratio >= SYNC_RATIO_THRESHOLD && length(detected_bits) >= PACKET_MIN_LENGTH
                        packet_count = packet_count + 1;

                        % 통계 업데이트
                        G_ViterbiStats.confidence_history = [G_ViterbiStats.confidence_history, confidence];
                        G_ViterbiStats.packet_positions = [G_ViterbiStats.packet_positions, position + peak_pos];

                        fprintf('패킷 #%d 검출: 위치=%d, 길이=%d, 신뢰도=%.3f, 동기율=%.1f%%\n', ...
                            length(G_ViterbiStats.confidence_history), position + peak_pos, ...
                            length(detected_bits), confidence, sync_ratio*100);
                    end
                end
            end
        end
    end
end

function [detected_bits, confidence, viterbi_state] = viterbi_mlsd_decode(signal, viterbi_state)
    global VITERBI_TRACEBACK_DEPTH;

    N = length(signal);
    if N < 16
        detected_bits = [];
        confidence = 0;
        return;
    end

    % 4-상태 Viterbi MLSD (GMSK)
    num_states = 4;
    INF = 1e10;

    % 경로 메트릭 초기화
    path_metrics = INF * ones(num_states, N+1);
    path_metrics(1, 1) = 0;  % 초기 상태

    % 생존 경로 저장
    survivor_paths = zeros(num_states, N);
    survivor_bits = zeros(num_states, N);

    % 신호 레벨 정의
    signal_levels = [-1, +1];

    % Viterbi 알고리즘 메인 루프
    for t = 1:N
        new_path_metrics = INF * ones(num_states, 1);
        new_survivor_paths = zeros(num_states, 1);
        new_survivor_bits = zeros(num_states, 1);

        for curr_state = 1:num_states
            best_metric = INF;
            best_prev_state = 1;
            best_bit = 0;

            for prev_state = 1:num_states
                for input_bit = 0:1
                    % 상태 전이 계산
                    [next_state, is_valid] = compute_gmsk_state_transition(prev_state, input_bit);

                    if is_valid && next_state == curr_state
                        % 예상 신호 계산
                        expected_signal = compute_gmsk_expected_signal(prev_state, curr_state, ...
                            viterbi_state.h0, viterbi_state.h1, viterbi_state.bias);

                        % 브랜치 메트릭
                        error = signal(t) - expected_signal;
                        branch_metric = (error^2) / (2 * viterbi_state.noise_var);

                        % 총 경로 메트릭
                        total_metric = path_metrics(prev_state, t) + branch_metric;

                        if total_metric < best_metric
                            best_metric = total_metric;
                            best_prev_state = prev_state;
                            best_bit = input_bit;
                        end
                    end
                end
            end

            new_path_metrics(curr_state) = best_metric;
            new_survivor_paths(curr_state) = best_prev_state;
            new_survivor_bits(curr_state) = best_bit;
        end

        path_metrics(:, t+1) = new_path_metrics;
        survivor_paths(:, t) = new_survivor_paths;
        survivor_bits(:, t) = new_survivor_bits;
    end

    % 역추적
    [min_metric, best_final_state] = min(path_metrics(:, end));
    traceback_length = min(N, VITERBI_TRACEBACK_DEPTH);
    detected_bits = zeros(1, traceback_length);
    current_state = best_final_state;

    for t = traceback_length:-1:1
        detected_bits(t) = survivor_bits(current_state, t);
        current_state = survivor_paths(current_state, t);
    end

    % 신뢰도 계산
    if min_metric < INF
        normalized_metric = min_metric / traceback_length;
        confidence = exp(-normalized_metric / viterbi_state.noise_var);
        confidence = max(0.01, min(1.0, confidence));
    else
        confidence = 0.01;
    end

    % 채널 계수 적응형 업데이트 (간단한 LMS)
    if confidence > 0.3
        update_rate = 0.01;
        viterbi_state.noise_var = (1-update_rate) * viterbi_state.noise_var + update_rate * var(signal);
    end
end

function [next_state, is_valid] = compute_gmsk_state_transition(prev_state, input_bit)
    % GMSK 상태 전이 계산
    % 상태: 1=[-1,0], 2=[-1,π], 3=[+1,0], 4=[+1,π]

    is_valid = true;

    % 이전 심벌 추출
    if prev_state <= 2
        prev_symbol = -1;
    else
        prev_symbol = +1;
    end

    % 현재 심벌 계산 (NRZI)
    if input_bit == 0
        curr_symbol = prev_symbol;
    else
        curr_symbol = -prev_symbol;
    end

    % 위상 변화 계산
    if input_bit == 0
        if prev_state == 1 || prev_state == 3
            phase_change = 0;
        else
            phase_change = 1;
        end
    else
        if prev_state == 1 || prev_state == 3
            phase_change = 1;
        else
            phase_change = 0;
        end
    end

    % 다음 상태 결정
    if curr_symbol == -1
        if phase_change == 0
            next_state = 1;
        else
            next_state = 2;
        end
    else
        if phase_change == 0
            next_state = 3;
        else
            next_state = 4;
        end
    end
end

function expected_signal = compute_gmsk_expected_signal(prev_state, curr_state, h0, h1, bias)
    % GMSK 예상 신호 계산

    % 이전 심벌
    if prev_state <= 2
        prev_symbol = -1;
    else
        prev_symbol = +1;
    end

    % 현재 심벌
    if curr_state <= 2
        curr_symbol = -1;
    else
        curr_symbol = +1;
    end

    % 채널 모델
    expected_signal = h0 * curr_symbol + h1 * prev_symbol + bias;
end

function update_plots(data_buffer, filtered_buffer, correlation_buffer, packet_count, buffer_count)
    % Plot 업데이트 함수
    global SAMPLE_RATE BUFFER_SIZE;

    try
        % 시간 축 생성
        time_axis = (0:length(data_buffer)-1) / SAMPLE_RATE;

        % 입력 신호
        subplot(3,2,1);
        if length(data_buffer) > 1000
            plot_data = data_buffer(end-999:end);
            plot_time = time_axis(end-999:end);
        else
            plot_data = data_buffer;
            plot_time = time_axis;
        end
        plot(plot_time, plot_data, 'b-');
        title('입력 신호');
        xlabel('시간 (초)');
        ylabel('진폭');
        grid on;

        % 필터링된 신호
        subplot(3,2,2);
        if length(filtered_buffer) > 1000
            plot_filtered = filtered_buffer(end-999:end);
            plot_time_f = (0:length(plot_filtered)-1) / SAMPLE_RATE;
        else
            plot_filtered = filtered_buffer;
            plot_time_f = (0:length(plot_filtered)-1) / SAMPLE_RATE;
        end
        plot(plot_time_f, real(plot_filtered), 'r-');
        title('필터링된 신호');
        xlabel('시간 (초)');
        ylabel('진폭');
        grid on;

        % 상관 결과
        subplot(3,2,3);
        if ~isempty(correlation_buffer)
            if length(correlation_buffer) > 1000
                plot_corr = correlation_buffer(end-999:end);
            else
                plot_corr = correlation_buffer;
            end
            plot(plot_corr, 'g-');
            title('상관 결과');
            xlabel('샘플');
            ylabel('상관값');
            grid on;
        end

        % Viterbi 신뢰도
        subplot(3,2,4);
        global G_ViterbiStats;
        if ~isempty(G_ViterbiStats.confidence_history)
            plot(G_ViterbiStats.confidence_history, 'mo-');
            title('Viterbi 신뢰도');
            xlabel('패킷');
            ylabel('신뢰도');
            grid on;
        end

        % 검출된 패킷 수
        subplot(3,2,5);
        elapsed_time = buffer_count * BUFFER_SIZE / SAMPLE_RATE;
        bar(elapsed_time, packet_count, 'FaceColor', 'cyan');
        title(sprintf('검출된 패킷 수: %d개', packet_count));
        xlabel('시간 (초)');
        ylabel('패킷 수');
        grid on;

        % 스펙트럼
        subplot(3,2,6);
        if length(data_buffer) >= 512
            [psd, freq] = pwelch(data_buffer(end-511:end), [], [], [], SAMPLE_RATE);
            semilogy(freq, psd, 'k-');
            title('신호 스펙트럼');
            xlabel('주파수 (Hz)');
            ylabel('PSD');
            grid on;
        end

        drawnow;

    catch ME
        % Plot 오류 시 무시하고 계속 진행
        fprintf('Plot 업데이트 오류: %s\n', ME.message);
    end
end
