%------------------------------------------------------------------------
% AIS Receiver with Full Viterbi MLSD Implementation
% 전체 데이터 수집에 Viterbi MLSD 알고리즘 적용
% Created: 2024-07-14
%------------------------------------------------------------------------

clear all;
close all;

%------------------------------------------------------------------------
% 전역 변수 선언
%------------------------------------------------------------------------
global G_pFilteredData G_pSrcDataCh1 G_ViterbiState G_ViterbiStats;
global VITERBI_PACKET_LENGTH VITERBI_CONFIDENCE_THRESHOLD;
global G_dCrcErrCnt G_dCRCErrSymIdx ENABLE_PLOT99 ENABLE_PLOT3;

%------------------------------------------------------------------------
% 설정 파라미터
%------------------------------------------------------------------------
ENABLE_VITERBI_MLSD     = 1;                % 0: Disable, 1: Enable Full Viterbi MLSD
ENABLE_PLOT1            = 0;                % 0: Disable, 1: Enable
ENABLE_PLOT2            = 0;                % 0: Disable, 1: Enable
ENABLE_PLOT3            = 1;                % 0: Disable, 1: Enable (성공 패킷)
ENABLE_PLOT99           = 1;                % 0: Disable, 1: Enable (오류 패킷)
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: Enable

% Viterbi MLSD 파라미터
VITERBI_CONFIDENCE_THRESHOLD = 0.3;         % Viterbi 신뢰도 임계값
VITERBI_PACKET_LENGTH = 184;                % AIS 패킷 길이 (168 + 16 CRC)

%------------------------------------------------------------------------
% 상수 정의
%------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_PRELOAD   = 1;
RX_MDM_STATUS_DATA      = 2;

SYNC_DETECT_OFFSET      = 24;
DC_MID_LEVEL           = 0;

%------------------------------------------------------------------------
% 전역 변수 초기화
%------------------------------------------------------------------------
G_dSyncDetCnt    = 0;
G_dAdcErrCnt     = 0;
G_dStartErrCnt   = 0;
G_dStuffErrCnt   = 0;
G_dCrcErrCnt     = 0;
G_dRcvPktCnt     = 0;

G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
G_wRxShiftReg    = 0;
G_wRxBitCount    = 0;
G_bRxByteData    = 0;
G_wCrcRegData    = 0xffff;
G_wRxReferValue  = DC_MID_LEVEL;
G_PreStart       = 1;
G_PreOffset      = 100;

% 데이터 배열
G_BitDataArray   = zeros(1, 1000);
G_dCRCErrSymIdx  = zeros(1, 1000);

% Viterbi MLSD 전용 변수
G_ViterbiStats = struct();
G_ViterbiStats.total_packets = 0;
G_ViterbiStats.successful_packets = 0;
G_ViterbiStats.confidence_history = [];
G_ViterbiStats.crc_success_history = [];

% Viterbi 상태 정보
G_ViterbiState = struct();
G_ViterbiState.h0 = 0.8;           % 채널 계수
G_ViterbiState.h1 = -0.4;          % 채널 계수  
G_ViterbiState.bias = 0.0;         % DC 바이어스
G_ViterbiState.noise_var = 0.1;    % 노이즈 분산

%------------------------------------------------------------------------
% 데이터 로드
%------------------------------------------------------------------------
fprintf('AIS 데이터 로딩 중...\n');

% 데이터 파일 읽기
fid = fopen('DumpData/DUMPDATA_250525_ch2.bin', 'rb');
if fid == -1
    error('데이터 파일을 열 수 없습니다: DUMPDATA_250525_ch2.bin');
end

G_pSrcDataCh1 = fread(fid, inf, 'int16');
fclose(fid);

if isempty(G_pSrcDataCh1)
    error('데이터 파일이 비어있습니다.');
end

fprintf('데이터 로드 완료: %d 샘플\n', length(G_pSrcDataCh1));

% 데이터 정규화
G_pSrcDataCh1 = double(G_pSrcDataCh1) / 32768.0;

%------------------------------------------------------------------------
% 필터 초기화
%------------------------------------------------------------------------
if (ENABLE_GMSK_RX_FLT > 0)
    % GMSK 수신 필터 설정
    filter_taps = 64;
    G_pFilteredData = filter(ones(1, filter_taps)/filter_taps, 1, G_pSrcDataCh1);
    fprintf('GMSK 수신 필터 적용 완료\n');
else
    G_pFilteredData = G_pSrcDataCh1;
end

%------------------------------------------------------------------------
% 메인 처리 루프
%------------------------------------------------------------------------
fprintf('\n=== Viterbi MLSD AIS 수신기 시작 ===\n');
fprintf('전체 샘플 수: %d\n', length(G_pSrcDataCh1));

nCurSymbolIdx = 1;
nDataLength = length(G_pSrcDataCh1);

% 동적 DC 오프셋 계산용
AdativeDcOffset = zeros(1, nDataLength);
BitArray = zeros(1, nDataLength);

% 메인 처리 루프
for idx = 1:nDataLength
    if mod(idx, 100000) == 0
        fprintf('처리 진행률: %.1f%%\n', (idx/nDataLength)*100);
    end
    
    % 현재 신호값
    if (ENABLE_GMSK_RX_FLT > 0)
        current_signal = G_pFilteredData(idx);
    else
        current_signal = G_pSrcDataCh1(idx);
    end
    
    % 동적 DC 오프셋 계산
    if idx > 100
        AdativeDcOffset(idx) = mean(G_pFilteredData(max(1, idx-50):idx));
    else
        AdativeDcOffset(idx) = mean(G_pFilteredData(1:idx));
    end
    
    % 비트 결정
    if current_signal > AdativeDcOffset(idx)
        BitArray(idx) = 1;
    else
        BitArray(idx) = 0;
    end
    
    % 상태별 처리
    switch G_wRxRunStatus
        case RX_MDM_STATUS_PREAMBLE
            % 프리앰블 및 동기 패턴 검출
            [sync_detected, nCurSymbolIdx] = detect_sync_pattern(idx, BitArray);
            
            if sync_detected
                G_dSyncDetCnt = G_dSyncDetCnt + 1;
                
                % 패킷 시작 위치 설정
                if (nCurSymbolIdx > G_PreOffset)
                    G_PreStart = nCurSymbolIdx - G_PreOffset;
                else
                    G_PreStart = 1;
                end
                
                fprintf('동기 패턴 검출 #%d (위치: %d)\n', G_dSyncDetCnt, nCurSymbolIdx);
                
                % Viterbi MLSD 데이터 수집 시작
                if ENABLE_VITERBI_MLSD == 1
                    [packet_success, packet_data] = viterbi_mlsd_packet_decode(idx, G_PreStart, nDataLength);
                    
                    if packet_success
                        G_dRcvPktCnt = G_dRcvPktCnt + 1;
                        G_ViterbiStats.successful_packets = G_ViterbiStats.successful_packets + 1;
                        
                        fprintf('✓ Viterbi MLSD 패킷 수신 성공 #%d\n', G_dRcvPktCnt);
                        
                        % 성공 패킷 플롯
                        if (ENABLE_PLOT3 == 1)
                            plot_successful_packet(G_PreStart, idx, nCurSymbolIdx);
                        end
                    else
                        % Viterbi 실패 시 오류 처리
                        handle_viterbi_failure(G_PreStart, idx, nCurSymbolIdx);
                    end
                    
                    G_ViterbiStats.total_packets = G_ViterbiStats.total_packets + 1;
                else
                    % 기존 방식으로 처리 (비교용)
                    G_wRxRunStatus = RX_MDM_STATUS_DATA;
                    G_wRxBitCount = 0;
                    G_bRxByteData = 0;
                    G_wCrcRegData = 0xffff;
                end
                
                % 상태 리셋
                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
            end
            
        case RX_MDM_STATUS_DATA
            % 기존 방식 데이터 처리 (비교용)
            % 이 부분은 ENABLE_VITERBI_MLSD == 0일 때만 사용
            
        otherwise
            % 기타 상태 처리
    end
end

%------------------------------------------------------------------------
% 결과 출력
%------------------------------------------------------------------------
fprintf('\n=== AIS 수신기 성능 결과 ===\n');
fprintf('동기 검출: %d개\n', G_dSyncDetCnt);
fprintf('성공 패킷: %d개\n', G_dRcvPktCnt);

if ENABLE_VITERBI_MLSD == 1
    fprintf('\n=== Viterbi MLSD 성능 ===\n');
    fprintf('총 처리 패킷: %d개\n', G_ViterbiStats.total_packets);
    fprintf('성공 패킷: %d개\n', G_ViterbiStats.successful_packets);
    if G_ViterbiStats.total_packets > 0
        success_rate = (G_ViterbiStats.successful_packets / G_ViterbiStats.total_packets) * 100;
        fprintf('성공률: %.1f%%\n', success_rate);
    end
    
    if ~isempty(G_ViterbiStats.confidence_history)
        fprintf('평균 신뢰도: %.3f\n', mean(G_ViterbiStats.confidence_history));
        fprintf('신뢰도 범위: %.3f ~ %.3f\n', ...
            min(G_ViterbiStats.confidence_history), max(G_ViterbiStats.confidence_history));
    end
end

%------------------------------------------------------------------------
% 성능 그래프
%------------------------------------------------------------------------
figure(9);
if ENABLE_VITERBI_MLSD == 1
    bar_x = ["SyncDet" "Viterbi Success" "Viterbi Fail"];
    bar_y = [G_dSyncDetCnt, G_ViterbiStats.successful_packets, ...
             G_ViterbiStats.total_packets - G_ViterbiStats.successful_packets];
    b = bar(bar_x, bar_y, 'FaceColor', 'flat');
    b.CData(2,:) = [0.0 0.8 0.0];  % 성공 (녹색)
    b.CData(3,:) = [0.8 0.0 0.0];  % 실패 (빨간색)
else
    bar_x = ["SyncDet" "Success"];
    bar_y = [G_dSyncDetCnt, G_dRcvPktCnt];
    b = bar(bar_x, bar_y, 'FaceColor', 'flat');
    b.CData(2,:) = [0.0 0.8 0.0];
end

xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')
title('AIS Receiver with Viterbi MLSD Performance');

fprintf('\n=== 처리 완료 ===\n');

%------------------------------------------------------------------------
% 동기 패턴 검출 함수
%------------------------------------------------------------------------
function [sync_detected, sync_position] = detect_sync_pattern(current_idx, bit_array)
    sync_detected = false;
    sync_position = current_idx;

    % AIS 동기 패턴: 01111110 (0x7E)
    sync_pattern = [0 1 1 1 1 1 1 0];
    pattern_length = length(sync_pattern);

    if current_idx >= pattern_length
        % 현재 위치에서 패턴 매칭
        current_pattern = bit_array(current_idx-pattern_length+1:current_idx);

        % 패턴 일치도 계산
        match_count = sum(current_pattern == sync_pattern);
        match_ratio = match_count / pattern_length;

        % 75% 이상 일치하면 동기 패턴으로 인식
        if match_ratio >= 0.75
            sync_detected = true;
            sync_position = current_idx;
        end
    end
end

%------------------------------------------------------------------------
% Viterbi MLSD 패킷 디코딩 (전체 패킷 처리)
%------------------------------------------------------------------------
function [success, packet_data] = viterbi_mlsd_packet_decode(current_idx, packet_start, data_length)
    global G_pFilteredData G_ViterbiState G_ViterbiStats VITERBI_PACKET_LENGTH VITERBI_CONFIDENCE_THRESHOLD;

    success = false;
    packet_data = [];

    % 패킷 신호 추출
    packet_end = min(data_length, current_idx + VITERBI_PACKET_LENGTH * 8);  % 오버샘플링 고려

    if packet_end <= packet_start
        return;
    end

    signal_segment = G_pFilteredData(packet_start:packet_end);

    % Viterbi MLSD 디코딩
    [decoded_bits, confidence] = viterbi_mlsd_decode(signal_segment);

    % 신뢰도 기록
    G_ViterbiStats.confidence_history = [G_ViterbiStats.confidence_history, confidence];

    if length(decoded_bits) >= VITERBI_PACKET_LENGTH && confidence > VITERBI_CONFIDENCE_THRESHOLD
        % Start bit 검증
        if check_start_bit(decoded_bits)
            % Stuff bit 처리
            [destuffed_bits, stuff_success] = remove_stuff_bits(decoded_bits);

            if stuff_success && length(destuffed_bits) >= 168
                % CRC 검증
                crc_result = calculate_packet_crc(destuffed_bits);

                if crc_result == 0xf0b8
                    success = true;
                    packet_data = destuffed_bits;
                    G_ViterbiStats.crc_success_history = [G_ViterbiStats.crc_success_history, 1];

                    fprintf('  ✓ Viterbi: 신뢰도=%.3f, CRC=OK\n', confidence);
                else
                    G_ViterbiStats.crc_success_history = [G_ViterbiStats.crc_success_history, 0];
                    fprintf('  ✗ Viterbi: 신뢰도=%.3f, CRC=0x%04x (≠0xf0b8)\n', confidence, crc_result);
                end
            else
                fprintf('  ✗ Viterbi: Stuff bit 처리 실패\n');
            end
        else
            fprintf('  ✗ Viterbi: Start bit 검증 실패\n');
        end
    else
        fprintf('  ✗ Viterbi: 신뢰도 부족 (%.3f < %.3f)\n', confidence, VITERBI_CONFIDENCE_THRESHOLD);
    end
end

%------------------------------------------------------------------------
% Viterbi MLSD 디코딩 알고리즘
%------------------------------------------------------------------------
function [decoded_bits, confidence] = viterbi_mlsd_decode(signal)
    % 신호 전처리
    signal = signal(:)';  % 행 벡터로 변환
    N = length(signal);

    if N < 100
        decoded_bits = [];
        confidence = 0;
        return;
    end

    % 신호 정규화
    dc_offset = mean(signal);
    signal_std = std(signal);
    if signal_std > 0
        normalized_signal = (signal - dc_offset) / signal_std;
    else
        normalized_signal = signal - dc_offset;
    end

    % 다운샘플링 (8:1 비율로 가정)
    downsample_ratio = max(1, floor(N / 200));  % 약 200비트 길이로 조정
    downsampled_length = floor(N / downsample_ratio);
    downsampled_signal = zeros(1, downsampled_length);

    for i = 1:downsampled_length
        start_idx = (i-1) * downsample_ratio + 1;
        end_idx = min(i * downsample_ratio, N);
        downsampled_signal(i) = mean(normalized_signal(start_idx:end_idx));
    end

    % 2-상태 Viterbi MLSD
    num_states = 2;
    path_metrics = [0, inf];  % [상태0, 상태1]

    decoded_bits = zeros(1, downsampled_length);
    total_error = 0;

    for t = 1:downsampled_length
        new_metrics = [inf, inf];
        new_decisions = [0, 0];

        current_signal = downsampled_signal(t);

        % 각 상태에서 각 입력에 대한 메트릭 계산
        for prev_state = 1:num_states
            if path_metrics(prev_state) < inf
                for input_bit = 0:1
                    % NRZI 디코딩: 0=변화없음, 1=변화있음
                    if input_bit == 0
                        next_state = prev_state;
                        if prev_state == 1
                            expected_level = -1;
                        else
                            expected_level = 1;
                        end
                    else
                        next_state = 3 - prev_state;  % 1↔2
                        if next_state == 1
                            expected_level = -1;
                        else
                            expected_level = 1;
                        end
                    end

                    % 브랜치 메트릭
                    error = current_signal - expected_level;
                    branch_metric = error^2;
                    total_metric = path_metrics(prev_state) + branch_metric;

                    if total_metric < new_metrics(next_state)
                        new_metrics(next_state) = total_metric;
                        new_decisions(next_state) = input_bit;
                    end
                end
            end
        end

        path_metrics = new_metrics;

        % 최적 결정
        [min_metric, best_state] = min(path_metrics);
        decoded_bits(t) = new_decisions(best_state);
        total_error = total_error + min_metric;
    end

    % 신뢰도 계산
    if downsampled_length > 0
        avg_error = total_error / downsampled_length;
        confidence = exp(-avg_error / 2);  % 정규화된 신뢰도
        confidence = max(0.01, min(0.99, confidence));
    else
        confidence = 0.01;
    end

    % 184비트로 조정
    if length(decoded_bits) > 184
        decoded_bits = decoded_bits(1:184);
    elseif length(decoded_bits) < 184
        decoded_bits = [decoded_bits, zeros(1, 184-length(decoded_bits))];
    end
end

%------------------------------------------------------------------------
% Start bit 검증
%------------------------------------------------------------------------
function is_valid = check_start_bit(bits)
    is_valid = false;

    if length(bits) >= 8
        % AIS start flag: 01111110
        start_pattern = [0 1 1 1 1 1 1 0];
        actual_start = bits(1:8);

        % 75% 이상 일치하면 유효
        match_count = sum(start_pattern == actual_start);
        if match_count >= 6
            is_valid = true;
        end
    end
end

%------------------------------------------------------------------------
% Stuff bit 제거
%------------------------------------------------------------------------
function [destuffed_bits, success] = remove_stuff_bits(bits)
    success = false;
    destuffed_bits = [];

    if length(bits) < 168
        return;
    end

    % Start flag 이후부터 stuff bit 제거
    data_start = 9;  % Start flag 이후
    input_bits = bits(data_start:end);
    output_bits = [];

    consecutive_ones = 0;
    i = 1;

    while i <= length(input_bits)
        current_bit = input_bits(i);
        output_bits = [output_bits, current_bit];

        if current_bit == 1
            consecutive_ones = consecutive_ones + 1;

            % 5개 연속 1 다음에 0이 오면 stuff bit로 제거
            if consecutive_ones == 5 && i < length(input_bits)
                if input_bits(i+1) == 0
                    i = i + 2;  % stuff bit(0) 건너뛰기
                    consecutive_ones = 0;
                    continue;
                end
            end
        else
            consecutive_ones = 0;
        end

        i = i + 1;
    end

    % 최소 168비트 확보
    if length(output_bits) >= 168
        destuffed_bits = output_bits;
        success = true;
    end
end

%------------------------------------------------------------------------
% CRC 계산
%------------------------------------------------------------------------
function crc_result = calculate_packet_crc(bits)
    if length(bits) < 168
        crc_result = 0x0000;
        return;
    end

    % 168비트 데이터에 대해 CRC 계산
    data_bits = bits(1:168);
    crc_result = 0xffff;

    for i = 1:length(data_bits)
        crc_result = update_crc(crc_result, data_bits(i));
    end
end

%------------------------------------------------------------------------
% CRC 업데이트 (기존 함수 사용)
%------------------------------------------------------------------------
function crc_out = update_crc(crc_in, data_bit)
    % AIS CRC-16 계산
    crc_out = crc_in;

    if bitand(bitxor(bitshift(crc_out, -15), data_bit), 0x0001)
        crc_out = bitxor(bitshift(bitand(crc_out, 0x7fff), 1), 0x1021);
    else
        crc_out = bitshift(bitand(crc_out, 0x7fff), 1);
    end

    crc_out = bitand(crc_out, 0xffff);
end

%------------------------------------------------------------------------
% Viterbi 실패 처리
%------------------------------------------------------------------------
function handle_viterbi_failure(packet_start, current_idx, sync_idx)
    global G_dCrcErrCnt G_dCRCErrSymIdx ENABLE_PLOT99 G_pFilteredData;

    G_dCrcErrCnt = G_dCrcErrCnt + 1;
    G_dCRCErrSymIdx(G_dCrcErrCnt) = packet_start;

    fprintf('✗ Viterbi MLSD 실패 #%d\n', G_dCrcErrCnt);

    % 실패 패킷 플롯
    if (ENABLE_PLOT99 == 1)
        h_fig99 = figure(99);
        h_fig99.Name = 'Viterbi MLSD Failed Packet';

        plot_range = packet_start:min(current_idx+100, length(G_pFilteredData));
        plot(plot_range, G_pFilteredData(plot_range), '-r');
        grid on;
        title('Viterbi MLSD Failed Packet');
        xlabel('Sample Index');
        ylabel('Signal Level');
    end
end

%------------------------------------------------------------------------
% 성공 패킷 플롯
%------------------------------------------------------------------------
function plot_successful_packet(packet_start, current_idx, sync_idx)
    global G_pFilteredData G_pSrcDataCh1;

    h_fig3 = figure(3);
    h_fig3.Name = 'Viterbi MLSD Successful Packet';

    plot_range = packet_start:min(current_idx+100, length(G_pFilteredData));
    plot(plot_range, G_pSrcDataCh1(plot_range), '-b', ...
         plot_range, G_pFilteredData(plot_range), '-g', 'LineWidth', 1.5);
    grid on;
    legend('Original', 'Filtered', 'Location', 'best');
    title('Viterbi MLSD Successful Packet');
    xlabel('Sample Index');
    ylabel('Signal Level');

    % 동기 위치 표시
    hold on;
    plot(sync_idx, G_pFilteredData(sync_idx), 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    hold off;
end
