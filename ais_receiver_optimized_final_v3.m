clear;

%-------------------------------------------------------------------------
% AIS 수신기 최적화 v3 - 고성능 Viterbi MLSD (PLL 제거)
%-------------------------------------------------------------------------
% v3 주요 개선사항:
% 1. PLL 타이밍 복구 완전 제거
% 2. 진정한 Viterbi MLSD 구현
% 3. 고급 채널 추정 및 추적
% 4. 소프트 결정 및 다중 가설 검증
% 5. 적응형 노이즈 추정
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data, 2: CRC Error Data
ENABLE_NOTCH_FLT        = 0;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 0;                % 0: Disable (PLL 제거로 DC offset 불필요)
ADAPTIVE_DC_METHOD      = 4;                % 1: Enhanced, 2: Kalman, 3: HPF, 4: Original
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable
ENABLE_FILE_SAVE        = 0;                % 0: Disable file saving, 1: Enable file saving

% 자동 성능 테스트 설정
ENABLE_AUTO_OPTIMIZATION = 0;               % 1: 자동 최적화 활성화, 0: 단일 테스트
AUTO_TEST_TARGET_PACKETS = 50;              % 목표 패킷 수 (현실적으로 조정)
AUTO_TEST_MAX_ITERATIONS = 30;              % 최대 테스트 반복 횟수

% 자동 테스트 파라미터 범위 정의
if ENABLE_AUTO_OPTIMIZATION
    % 윈도우 크기 범위 (더 긴 패킷 검출을 위해 큰 값들 추가)
    WINDOW_SIZE_RANGE = [32, 64, 96, 128, 168, 256];
    % Traceback 깊이 범위
    TRACEBACK_DEPTH_RANGE = [16, 24, 32, 48, 64];
    % 신뢰도 임계값 범위 (성공했던 값 중심)
    CONFIDENCE_THRESHOLD_RANGE = [0.01, 0.03, 0.05, 0.08, 0.1];
    % 채널 업데이트 비율 범위
    CHANNEL_UPDATE_RANGE = [0.1, 0.15, 0.2, 0.25, 0.3];
    % 초기 h0 값 범위 (성공했던 값 중심)
    INITIAL_H0_RANGE = [0.7, 0.8, 0.9, 1.0, 1.1];
    % 초기 h1 값 범위 (성공했던 값 중심)
    INITIAL_H1_RANGE = [-0.5, -0.4, -0.3, -0.2, -0.1];
    % 동기 일치율 임계값 범위 (성공했던 값 중심)
    SYNC_RATIO_RANGE = [0.4, 0.5, 0.6, 0.7, 0.75];

    % 기본값 (첫 번째 테스트용)
    VITERBI_WINDOW_SIZE = WINDOW_SIZE_RANGE(1);
    VITERBI_TRACEBACK_DEPTH = TRACEBACK_DEPTH_RANGE(1);
    VITERBI_CONFIDENCE_THRESHOLD = CONFIDENCE_THRESHOLD_RANGE(1);
    CHANNEL_UPDATE_RATE = CHANNEL_UPDATE_RANGE(1);
    VITERBI_INITIAL_H0 = INITIAL_H0_RANGE(1);
    VITERBI_INITIAL_H1 = INITIAL_H1_RANGE(1);
    SYNC_RATIO_THRESHOLD = SYNC_RATIO_RANGE(1);
else
    % 수동 설정 (AIS 패킷 전체 검출용)
    VITERBI_WINDOW_SIZE     = 256;              % AIS 최대 패킷 크기 (168~256비트)
    VITERBI_TRACEBACK_DEPTH = 256;              % 전체 패킷 역추적
    VITERBI_CONFIDENCE_THRESHOLD = 0.05;        % 신뢰도 임계값
    CHANNEL_UPDATE_RATE     = 0.15;             % 채널 업데이트 비율
    VITERBI_INITIAL_H0      = 0.8;              % 초기 h0 값
    VITERBI_INITIAL_H1      = -0.4;             % 초기 h1 값
    SYNC_RATIO_THRESHOLD    = 0.5;              % 동기 일치율 임계값
end

% 고성능 Viterbi MLSD 설정 (v3 개선)
ENABLE_VITERBI_MLSD     = 1;                % 0: Disable, 1: Enable True Viterbi MLSD
ENABLE_CHANNEL_ESTIMATION = 1;              % 0: Disable, 1: Enable Advanced Channel Estimation
VITERBI_ONLY_MODE       = 1;                % 1: 순수 Viterbi만 사용 (PLL 제거)
VITERBI_ERROR_CORRECTION = 1;               % 1: 고급 Viterbi 오류 정정 활성화
VITERBI_FALLBACK_TO_DC  = 0;                % 0: Viterbi 실패 시에도 Viterbi 결과 사용
VITERBI_INITIAL_BIAS    = 0.0;              % 초기 바이어스 값
VITERBI_NOISE_VARIANCE  = 0.005;            % 더 정확한 노이즈 분산 추정값

% 고급 Viterbi 성능 향상 파라미터 (v3 신규)
VITERBI_SOFT_DECISION   = 1;                % 1: 소프트 결정 활성화
VITERBI_ADAPTIVE_NOISE  = 1;                % 1: 적응형 노이즈 추정 활성화
VITERBI_MULTI_HYPOTHESIS = 1;               % 1: 다중 가설 검증 활성화
CHANNEL_ESTIMATION_WINDOW = 48;             % 채널 추정 윈도우 크기
CHANNEL_TRACKING_ENABLE = 1;                % 1: 실시간 채널 추적 활성화

% 기존 방법 최적화 파라미터 (백업용)
DC_BASE_ALPHA           = 0.922;            % 기본 알파값
DC_ALPHA_MIN            = 0.852;            % 최소 알파값
DC_ALPHA_MAX            = 0.991;            % 최대 알파값
DC_STABILITY_THRESHOLD  = 0.00028;          % DC 안정성 임계값
DC_VARIANCE_WINDOW      = 12;               % DC 변화량 윈도우 크기

%-------------------------------------------------------------------------
ENABLE_DEBUG            = 0;
ENABLE_FREQ_ANALYSIS    = 1;                % 0: Disable, 1: Enable Frequency Analysis Plots
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: NRZI decoding
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Bit stream
    ENABLE_PLOT4        = 1;                % 0: Disable, 1: Packet data
    ENABLE_PLOT5        = 1;                % 0: Disable, 1: Adaptive DC offset
    ENABLE_PLOT6        = 1;                % 0: Disable, 1: Viterbi MLSD
else
    ENABLE_PLOT1        = 0;
    ENABLE_PLOT2        = 0;
    ENABLE_PLOT3        = 0;
    ENABLE_PLOT4        = 0;
    ENABLE_PLOT5        = 0;
    ENABLE_PLOT6        = 0;
end

%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
% Sync detection
%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = 0.75;             % Sync detection threshold
MAX_SYNC_COUNT          = 25;               % Max sync detection count

%-------------------------------------------------------------------------
% NRZI decoding
%-------------------------------------------------------------------------
NRZI_HYSTERESIS         = 0.02;             % NRZI 히스테리시스 값

%-------------------------------------------------------------------------
% Adaptive DC offset
%-------------------------------------------------------------------------
DC_AVG_COUNT            = 55;               % DC offset averaging count
ADC_SUB_DC_OFFSET       = 1450;             % ADC DC offset subtraction

%-------------------------------------------------------------------------
% Packet detection
%-------------------------------------------------------------------------
PACKET_SYNC_PATTERN     = [0,1,1,1,1,1,1,0]; % AIS sync pattern
PACKET_MIN_LENGTH       = 168;              % Minimum packet length (bits)
PACKET_MAX_LENGTH       = 256;              % Maximum packet length (bits)

%-------------------------------------------------------------------------
% State machine
%-------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_START     = 1;
RX_MDM_STATUS_DATA      = 2;

%-------------------------------------------------------------------------
% PLL (제거됨 - v3에서는 Viterbi MLSD만 사용)
%-------------------------------------------------------------------------
% PLL 관련 변수들은 v3에서 완전히 제거되었습니다.
% 대신 Viterbi MLSD가 타이밍 복구와 심벌 검출을 모두 담당합니다.

%-------------------------------------------------------------------------
% File I/O - 스마트 파일 검색
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    filename = './DumpData/DUMPDATA_250525_ch1.bin';
elseif (USE_CHx_RAW_DATA == 1)
    filename = './DumpData/DUMPDATA_250525_ch2.bin';
else
    % CRC 오류 데이터 파일 자동 검색
    filename = '';

    % 1. 기본 파일명 확인
    if exist('crc_error_data_combined.bin', 'file')
        filename = 'crc_error_data_combined.bin';
    else
        % 2. CRC_Error_Data 폴더에서 최신 파일 검색
        crc_folders = dir('CRC_Error_Data_*');
        if ~isempty(crc_folders)
            % 가장 최근 폴더 선택
            [~, latest_idx] = max([crc_folders.datenum]);
            latest_folder = crc_folders(latest_idx).name;

            % 폴더 내 .bin 파일 검색
            bin_files = dir(fullfile(latest_folder, '*.bin'));
            if ~isempty(bin_files)
                filename = fullfile(latest_folder, bin_files(1).name);
                fprintf('CRC 오류 데이터 파일 자동 선택: %s\n', filename);
            end
        end

        % 3. DumpData 폴더에서 대체 파일 검색
        if isempty(filename)
            dump_files = {'DumpData/DUMPDATA_250525_ch1.bin', 'DumpData/DUMPDATA_250521_ch1.bin', ...
                         'DumpData/AisDumpData_ch1.bin', 'DumpData/rst.bin'};
            for i = 1:length(dump_files)
                if exist(dump_files{i}, 'file')
                    filename = dump_files{i};
                    fprintf('대체 데이터 파일 사용: %s\n', filename);
                    break;
                end
            end
        end
    end

    if isempty(filename)
        error('사용 가능한 데이터 파일을 찾을 수 없습니다!\n사용 가능한 파일:\n- crc_error_data_combined.bin\n- CRC_Error_Data_* 폴더의 .bin 파일\n- DumpData 폴더의 .bin 파일');
    end
end

if ~exist(filename, 'file')
    error('File %s not found!', filename);
end

%-------------------------------------------------------------------------
% Load data
%-------------------------------------------------------------------------
fprintf('Loading data from %s...\n', filename);
fid = fopen(filename, 'rb');
if fid == -1
    error('Cannot open file %s', filename);
end

G_pSrcDataCh1 = fread(fid, inf, 'uint16');
fclose(fid);

fprintf('Loaded %d samples\n', length(G_pSrcDataCh1));

if isempty(G_pSrcDataCh1)
    error('No data loaded from file');
end

%-------------------------------------------------------------------------
% 고급 Viterbi MLSD 함수들 (v3 신규)
%-------------------------------------------------------------------------

%------------------------------------------------------------------------
% 진정한 Viterbi MLSD 알고리즘 (완전 재작성)
function [detected_bits, path_metrics, confidence] = viterbi_mlsd_v3(received_signal, h0, h1, bias, window_size, traceback_depth, noise_variance)
    % 진정한 Viterbi MLSD - GMSK 채널에 최적화
    % received_signal: 수신 신호
    % h0, h1: 채널 임펄스 응답 계수
    % bias: DC 바이어스
    % window_size: 처리할 윈도우 크기
    % traceback_depth: 역추적 깊이
    % noise_variance: 노이즈 분산

    N = min(window_size, length(received_signal));
    if N < 8
        detected_bits = [];
        path_metrics = [];
        confidence = 0;
        return;
    end

    % GMSK는 메모리 길이 L=1인 CPM이므로 4개 상태 필요
    % 상태: [이전 심벌, 현재 위상 상태]
    % 상태 1: s(n-1)=-1, 위상=0
    % 상태 2: s(n-1)=-1, 위상=π
    % 상태 3: s(n-1)=+1, 위상=0
    % 상태 4: s(n-1)=+1, 위상=π
    num_states = 4;
    INF = 1e10;

    % 경로 메트릭 초기화
    path_metrics = INF * ones(num_states, N+1);
    path_metrics(1, 1) = 0;  % 초기 상태

    % 생존 경로 저장
    survivor_paths = zeros(num_states, N);
    survivor_bits = zeros(num_states, N);

    % GMSK 신호 레벨 (Laurent 분해 기반)
    % s(t) = h0*a(t) + h1*a(t-1) + bias
    % a(t) ∈ {-1, +1}

    % 노이즈 분산 정규화
    if nargin < 7 || noise_variance <= 0
        noise_variance = var(received_signal) * 0.1;
    end
    noise_variance = max(0.001, noise_variance);

    % Viterbi 알고리즘 메인 루프
    for t = 1:N
        new_path_metrics = INF * ones(num_states, 1);
        new_survivor_paths = zeros(num_states, 1);
        new_survivor_bits = zeros(num_states, 1);

        % 각 현재 상태에 대해
        for curr_state = 1:num_states
            best_metric = INF;
            best_prev_state = 1;
            best_input_bit = 0;

            % 모든 이전 상태에서의 전이 검사
            for prev_state = 1:num_states
                for input_bit = 0:1
                    % 상태 전이 계산
                    [next_state, is_valid] = compute_state_transition(prev_state, input_bit);

                    if is_valid && next_state == curr_state
                        % 예상 신호 계산 (GMSK 채널 모델)
                        expected_signal = compute_expected_signal(prev_state, curr_state, input_bit, h0, h1, bias);

                        % 브랜치 메트릭 (로그 우도)
                        error = received_signal(t) - expected_signal;
                        branch_metric = (error^2) / (2 * noise_variance);

                        % 총 경로 메트릭
                        total_metric = path_metrics(prev_state, t) + branch_metric;

                        % 최적 경로 선택
                        if total_metric < best_metric
                            best_metric = total_metric;
                            best_prev_state = prev_state;
                            best_input_bit = input_bit;
                        end
                    end
                end
            end

            % 새로운 경로 메트릭 및 생존 경로 저장
            new_path_metrics(curr_state) = best_metric;
            new_survivor_paths(curr_state) = best_prev_state;
            new_survivor_bits(curr_state) = best_input_bit;
        end

        % 업데이트
        path_metrics(:, t+1) = new_path_metrics;
        survivor_paths(:, t) = new_survivor_paths;
        survivor_bits(:, t) = new_survivor_bits;
    end

    % 역추적 (Traceback)
    [min_metric, best_final_state] = min(path_metrics(:, end));

    % 전체 시퀀스 역추적
    traceback_length = min(N, traceback_depth);
    detected_bits = zeros(1, traceback_length);
    current_state = best_final_state;

    for t = traceback_length:-1:1
        detected_bits(t) = survivor_bits(current_state, t);
        current_state = survivor_paths(current_state, t);
    end

    % 신뢰도 계산 (경로 메트릭 기반)
    if min_metric < INF
        % 정규화된 메트릭으로 신뢰도 계산
        normalized_metric = min_metric / traceback_length;
        confidence = exp(-normalized_metric / noise_variance);
        confidence = max(0.01, min(1.0, confidence));
    else
        confidence = 0.01;
    end

    % 최종 검증
    if traceback_length > 8
        validation_score = validate_gmsk_sequence(detected_bits, received_signal(1:traceback_length), h0, h1, bias);
        confidence = confidence * validation_score;
    end
end

%------------------------------------------------------------------------
% GMSK 상태 전이 계산
function [next_state, is_valid] = compute_state_transition(prev_state, input_bit)
    % GMSK 상태 전이 규칙
    % 상태: [이전 심벌, 위상]
    % 1: [-1, 0], 2: [-1, π], 3: [+1, 0], 4: [+1, π]

    is_valid = true;

    % 이전 상태에서 이전 심벌 추출
    if prev_state <= 2
        prev_symbol = -1;
    else
        prev_symbol = +1;
    end

    % 현재 심벌 계산 (NRZI 디코딩)
    if input_bit == 0
        curr_symbol = prev_symbol;  % 변화 없음
    else
        curr_symbol = -prev_symbol; % 반전
    end

    % 위상 변화 계산 (GMSK 특성)
    if input_bit == 0
        % 위상 변화 없음
        if prev_state == 1 || prev_state == 3
            phase_change = 0;  % 0 → 0
        else
            phase_change = 1;  % π → π
        end
    else
        % 위상 π/2 변화
        if prev_state == 1 || prev_state == 3
            phase_change = 1;  % 0 → π
        else
            phase_change = 0;  % π → 0
        end
    end

    % 다음 상태 결정
    if curr_symbol == -1
        if phase_change == 0
            next_state = 1;  % [-1, 0]
        else
            next_state = 2;  % [-1, π]
        end
    else
        if phase_change == 0
            next_state = 3;  % [+1, 0]
        else
            next_state = 4;  % [+1, π]
        end
    end
end

%------------------------------------------------------------------------
% GMSK 예상 신호 계산
function expected_signal = compute_expected_signal(prev_state, curr_state, ~, h0, h1, bias)
    % GMSK 채널 모델에 따른 예상 신호 계산

    % 이전 심벌 추출
    if prev_state <= 2
        prev_symbol = -1;
    else
        prev_symbol = +1;
    end

    % 현재 심벌 추출
    if curr_state <= 2
        curr_symbol = -1;
    else
        curr_symbol = +1;
    end

    % 채널 모델: y(t) = h0*s(t) + h1*s(t-1) + bias
    expected_signal = h0 * curr_symbol + h1 * prev_symbol + bias;
end

%------------------------------------------------------------------------
% GMSK 시퀀스 검증
function validation_score = validate_gmsk_sequence(bits, signal, h0, h1, bias)
    % GMSK 특성을 고려한 시퀀스 검증

    if length(bits) < 4
        validation_score = 0.5;
        return;
    end

    % 비트를 NRZI 심벌로 변환
    symbols = zeros(size(bits));
    prev_symbol = -1;  % 초기값

    for i = 1:length(bits)
        if bits(i) == 0
            symbols(i) = prev_symbol;  % 변화 없음
        else
            symbols(i) = -prev_symbol; % 반전
        end
        prev_symbol = symbols(i);
    end

    % 예상 신호 재구성
    reconstructed = zeros(size(signal));
    for i = 1:length(signal)
        if i == 1
            reconstructed(i) = h0 * symbols(i) + bias;
        else
            reconstructed(i) = h0 * symbols(i) + h1 * symbols(i-1) + bias;
        end
    end

    % 상관계수 계산
    if length(signal) == length(reconstructed)
        correlation_matrix = corrcoef(signal, reconstructed);
        if size(correlation_matrix, 1) > 1
            correlation = abs(correlation_matrix(1,2));
        else
            correlation = 0.5;
        end
    else
        % MSE 기반 검증
        min_len = min(length(signal), length(reconstructed));
        mse = mean((signal(1:min_len) - reconstructed(1:min_len)).^2);
        correlation = exp(-mse / var(signal));
    end

    % GMSK 특성 검증 (연속 동일 비트 제한)
    max_consecutive = check_consecutive_bits(bits);
    if max_consecutive > 5
        consecutive_penalty = 0.5;
    else
        consecutive_penalty = 1.0;
    end

    validation_score = correlation * consecutive_penalty;
    validation_score = max(0.1, min(1.0, validation_score));
end

%------------------------------------------------------------------------
% 연속 비트 검사
function max_consecutive = check_consecutive_bits(bits)
    max_consecutive = 1;
    current_consecutive = 1;

    for i = 2:length(bits)
        if bits(i) == bits(i-1)
            current_consecutive = current_consecutive + 1;
            max_consecutive = max(max_consecutive, current_consecutive);
        else
            current_consecutive = 1;
        end
    end
end



%------------------------------------------------------------------------
% 고급 채널 추정 함수 (v3 개선)
function [h0, h1, bias, confidence] = estimate_channel_v3(received_signal, known_bits, prev_h0, prev_h1, prev_bias)
    % v3 고급 채널 추정 - 적응형 및 강건한 추정
    % received_signal: 수신된 신호 (실수값)
    % known_bits: 알려진 비트 패턴 (0, 1)
    % prev_h0, prev_h1, prev_bias: 이전 채널 계수들

    N = min(length(received_signal), length(known_bits));
    if N < 16
        h0 = prev_h0;
        h1 = prev_h1;
        bias = prev_bias;
        confidence = 0.1;
        return;
    end

    % 비트를 NRZI로 변환 후 -1, +1로 매핑
    nrzi_signal = zeros(size(known_bits));
    prev_nrzi = 0;
    for i = 1:length(known_bits)
        if known_bits(i) == 0
            nrzi_signal(i) = prev_nrzi;  % 0이면 이전 상태 유지
        else
            nrzi_signal(i) = 1 - prev_nrzi;  % 1이면 반전
        end
        prev_nrzi = nrzi_signal(i);
    end

    % 신호 레벨로 변환 (-1, +1)
    signal_levels = 2 * nrzi_signal - 1;

    % 채널 모델: y(n) = h0*x(n) + h1*x(n-1) + bias + noise
    % 최소자승법을 위한 행렬 구성
    A = zeros(N-1, 3);  % [h0, h1, bias]
    b = received_signal(2:N);

    for i = 1:N-1
        A(i, 1) = signal_levels(i+1);      % h0 * x(n)
        A(i, 2) = signal_levels(i);        % h1 * x(n-1)
        A(i, 3) = 1;                       % bias
    end

    % 강건한 최소자승법 (outlier 제거)
    try
        % 초기 추정
        coeffs = A \ b;

        % 잔차 계산 및 outlier 검출
        residuals = b - A * coeffs;
        residual_std = std(residuals);
        outlier_threshold = 2.5 * residual_std;

        % outlier가 아닌 데이터만 선택
        valid_indices = abs(residuals) <= outlier_threshold;

        if sum(valid_indices) >= 8  % 충분한 데이터가 있으면
            A_clean = A(valid_indices, :);
            b_clean = b(valid_indices);
            coeffs = A_clean \ b_clean;
        end

        h0_new = coeffs(1);
        h1_new = coeffs(2);
        bias_new = coeffs(3);

        % 채널 계수 유효성 검사 (조건 대폭 완화)
        if abs(h0_new) > 0.001 && abs(h0_new) < 50.0 && abs(h1_new) < 20.0
            % 신뢰도 계산 (잔차 기반)
            final_residuals = b_clean - A_clean * coeffs;
            mse = mean(final_residuals.^2);
            confidence = exp(-mse * 10);  % MSE가 낮을수록 높은 신뢰도

            h0 = h0_new;
            h1 = h1_new;
            bias = bias_new;
        else
            % 유효하지 않으면 이전 값 유지
            h0 = prev_h0;
            h1 = prev_h1;
            bias = prev_bias;
            confidence = 0.1;
        end

    catch
        % 오류 발생 시 이전 값 유지
        h0 = prev_h0;
        h1 = prev_h1;
        bias = prev_bias;
        confidence = 0.1;
    end

    % 신뢰도 범위 제한
    confidence = max(0.1, min(1.0, confidence));
end

%------------------------------------------------------------------------
% 적응형 노이즈 추정 함수 (v3 신규)
function noise_variance = estimate_noise_variance_adaptive(received_signal, window_size)
    % 적응형 노이즈 분산 추정

    if nargin < 2
        window_size = 32;
    end

    N = length(received_signal);
    if N < window_size
        noise_variance = var(received_signal) * 0.1;
        return;
    end

    % 이동 윈도우를 사용한 지역적 분산 계산
    local_variances = zeros(1, N - window_size + 1);
    for i = 1:N - window_size + 1
        window_data = received_signal(i:i+window_size-1);
        local_variances(i) = var(window_data);
    end

    % 최소 분산을 노이즈 분산으로 추정 (신호가 없는 구간)
    noise_variance = min(local_variances);

    % 최소값 제한
    noise_variance = max(0.001, noise_variance);
end

%------------------------------------------------------------------------
% GMSK 필터 설계 함수
function h = design_gmsk_filter(BT, OSR, span)
    % GMSK 펄스 성형 필터 설계

    if nargin < 3
        span = 4;
    end

    % 시간 벡터
    t = (-span*OSR/2:span*OSR/2) / OSR;

    % GMSK 임펄스 응답
    alpha = sqrt(2*log(2)) / (2*pi*BT);
    h = 0.5 * (erf(alpha*(t+0.5)) - erf(alpha*(t-0.5)));

    % 정규화
    h = h / sum(h);
end

%------------------------------------------------------------------------
% 프리앰블 생성 함수
function preamble = generate_ais_preamble()
    % AIS 프리앰블 패턴 생성 (24비트)
    preamble = repmat([0,1], 1, 12);  % 010101...010101 (24비트)
end

%------------------------------------------------------------------------
% 동기 패턴 생성 함수
function sync_pattern = generate_ais_sync_pattern()
    % AIS 동기 패턴 생성 (NRZI 디코딩된 형태)
    % 원본: 01111110 → NRZI 디코딩: 01000001
    sync_pattern = [0,1,0,0,0,0,0,1];  % NRZI 디코딩된 AIS 동기 패턴
end

%------------------------------------------------------------------------
% CRC 계산 함수
function crc = calculate_crc16(data_bits)
    % CRC-16 계산 (AIS 표준)

    if isempty(data_bits)
        crc = 0;
        return;
    end

    crc = 0xFFFF;  % 초기값
    polynomial = 0x1021;  % CRC-16-CCITT 다항식

    for i = 1:length(data_bits)
        crc = bitxor(crc, bitshift(data_bits(i), 15));

        for j = 1:8
            if bitand(crc, 0x8000)
                crc = bitxor(bitshift(crc, 1), polynomial);
            else
                crc = bitshift(crc, 1);
            end
            crc = bitand(crc, 0xFFFF);
        end
    end

    crc = bitxor(crc, 0xFFFF);  % 최종 XOR
end

%------------------------------------------------------------------------
% 패킷 유효성 검사 함수
function is_valid = validate_ais_packet(bits)
    % AIS 패킷 유효성 검사

    is_valid = false;

    if length(bits) < 168  % 최소 AIS 패킷 길이
        return;
    end

    % 동기 패턴 검사
    sync_pattern = generate_ais_sync_pattern();
    if length(bits) >= length(sync_pattern)
        if isequal(bits(1:length(sync_pattern)), sync_pattern)
            is_valid = true;
        end
    end

    % 추가 검증 로직 (CRC 등)은 필요에 따라 구현
end

%------------------------------------------------------------------------
% 메인 처리 시작
%------------------------------------------------------------------------

fprintf('\n=== AIS 수신기 v3 - 고성능 Viterbi MLSD (PLL 제거) ===\n');
if ENABLE_VITERBI_MLSD
    fprintf('Viterbi MLSD: Enabled\n');
else
    fprintf('Viterbi MLSD: Disabled\n');
end
fprintf('Window Size: %d, Traceback Depth: %d\n', VITERBI_WINDOW_SIZE, VITERBI_TRACEBACK_DEPTH);
if ENABLE_CHANNEL_ESTIMATION
    fprintf('Channel Estimation: Enabled\n');
else
    fprintf('Channel Estimation: Disabled\n');
end
if VITERBI_SOFT_DECISION
    fprintf('Soft Decision: Enabled\n');
else
    fprintf('Soft Decision: Disabled\n');
end
if VITERBI_MULTI_HYPOTHESIS
    fprintf('Multi-Hypothesis: Enabled\n');
else
    fprintf('Multi-Hypothesis: Disabled\n');
end

%-------------------------------------------------------------------------
% 전처리
%-------------------------------------------------------------------------

% ADC 제한
if (ENABLE_ADC_LIMIT > 0)
    if (ENABLE_ADC_LIMIT == 1)
        G_pSrcDataCh1(G_pSrcDataCh1 < 0) = 0;
        G_pSrcDataCh1(G_pSrcDataCh1 > ADC_MAX_VALUE) = ADC_MAX_VALUE;
    else
        G_pSrcDataCh1(G_pSrcDataCh1 > ADC_MAX_VALUE) = ADC_MAX_VALUE;
    end
end

% DC 오프셋 제거
G_pSrcDataCh1 = G_pSrcDataCh1 - ADC_SUB_DC_OFFSET;

% GMSK 수신 필터 적용
if (ENABLE_GMSK_RX_FLT > 0)
    h_gmsk = design_gmsk_filter(RX_BT, OSR, 4);
    G_pFilteredData = conv(G_pSrcDataCh1, h_gmsk, 'same');
    fprintf('GMSK 필터 적용 완료\n');
else
    G_pFilteredData = G_pSrcDataCh1;
end

% 노치 필터 (필요시)
if (ENABLE_NOTCH_FLT > 0)
    % 노치 필터 구현 (생략)
    fprintf('노치 필터 적용 완료\n');
end

%-------------------------------------------------------------------------
% 프리앰블 및 동기 패턴 생성
%-------------------------------------------------------------------------
preamble_bits = generate_ais_preamble();
sync_pattern = generate_ais_sync_pattern();

% GMSK 변조된 프리앰블 생성 (상관 검출용)
h_tx = design_gmsk_filter(BT, OSR, 4);
preamble_upsampled = upsample(2*preamble_bits-1, OSR);  % -1, +1로 변환 후 업샘플링
G_vGmskPreamble = conv(preamble_upsampled, h_tx, 'same');

fprintf('프리앰블 길이: %d samples\n', length(G_vGmskPreamble));

%-------------------------------------------------------------------------
% Viterbi MLSD 관련 변수 초기화
%-------------------------------------------------------------------------
G_ViterbiEnabled = ENABLE_VITERBI_MLSD;
G_ChannelH0 = VITERBI_INITIAL_H0;
G_ChannelH1 = VITERBI_INITIAL_H1;
G_ChannelBias = VITERBI_INITIAL_BIAS;
G_NoiseVariance = VITERBI_NOISE_VARIANCE;

% 통계 변수들
G_ViterbiStats = struct();
G_ViterbiStats.total_packets = 0;
G_ViterbiStats.successful_packets = 0;
G_ViterbiStats.channel_estimations = 0;
G_ViterbiStats.avg_confidence = 0;
G_ViterbiStats.confidence_history = [];

% 채널 추적 변수들
G_ChannelHistory = struct();
G_ChannelHistory.h0 = [];
G_ChannelHistory.h1 = [];
G_ChannelHistory.bias = [];
G_ChannelHistory.confidence = [];

fprintf('Viterbi MLSD 초기화 완료\n');

%-------------------------------------------------------------------------
% 자동 성능 최적화 실행
%-------------------------------------------------------------------------
if ENABLE_AUTO_OPTIMIZATION
    fprintf('\n=== 자동 성능 최적화 시작 ===\n');
    fprintf('목표 패킷 수: %d\n', AUTO_TEST_TARGET_PACKETS);
    fprintf('최대 반복 횟수: %d\n', AUTO_TEST_MAX_ITERATIONS);

    % 최적화 결과 저장
    optimization_results = [];
    best_packet_count = 0;
    best_params = struct('window_size', 32, 'traceback_depth', 16, 'confidence_threshold', 0.05, ...
                        'channel_update_rate', 0.15, 'h0', 0.8, 'h1', -0.4, 'sync_ratio', 0.5);
    iteration_count = 0;

    % 파라미터 조합 생성 (스마트 탐색)
    param_combinations = generate_smart_parameter_combinations(WINDOW_SIZE_RANGE, TRACEBACK_DEPTH_RANGE, ...
        CONFIDENCE_THRESHOLD_RANGE, CHANNEL_UPDATE_RANGE, INITIAL_H0_RANGE, INITIAL_H1_RANGE, SYNC_RATIO_RANGE);

    for combo_idx = 1:min(length(param_combinations), AUTO_TEST_MAX_ITERATIONS)
        iteration_count = iteration_count + 1;
        current_params = param_combinations(combo_idx);

        fprintf('\n--- 반복 %d/%d ---\n', iteration_count, min(length(param_combinations), AUTO_TEST_MAX_ITERATIONS));
        fprintf('윈도우: %d, Traceback: %d, 신뢰도: %.3f, 채널업데이트: %.2f\n', ...
            current_params.window_size, current_params.traceback_depth, ...
            current_params.confidence_threshold, current_params.channel_update_rate);
        fprintf('h0: %.2f, h1: %.2f, 동기임계값: %.2f\n', ...
            current_params.h0, current_params.h1, current_params.sync_ratio);

        % 현재 파라미터로 테스트 실행
        packet_count = run_viterbi_test_with_params(current_params, G_pFilteredData, G_vGmskPreamble, sync_pattern);

        % 결과 저장
        result = current_params;
        result.packet_count = packet_count;
        optimization_results = [optimization_results; result];

        fprintf('검출된 패킷 수: %d\n', packet_count);

        % 최고 성능 업데이트
        if packet_count > best_packet_count
            best_packet_count = packet_count;
            best_params = current_params;
            fprintf('*** 새로운 최고 성능! ***\n');
        end

        % 목표 달성 시 조기 종료
        if packet_count >= AUTO_TEST_TARGET_PACKETS
            fprintf('*** 목표 패킷 수 달성! 최적화 완료 ***\n');
            break;
        end
    end

    % 최적화 결과 출력
    fprintf('\n=== 자동 최적화 결과 ===\n');
    fprintf('총 테스트 횟수: %d\n', iteration_count);
    fprintf('최고 패킷 수: %d\n', best_packet_count);
    fprintf('최적 파라미터:\n');
    fprintf('  윈도우 크기: %d\n', best_params.window_size);
    fprintf('  Traceback 깊이: %d\n', best_params.traceback_depth);
    fprintf('  신뢰도 임계값: %.3f\n', best_params.confidence_threshold);
    fprintf('  채널 업데이트 비율: %.2f\n', best_params.channel_update_rate);
    fprintf('  초기 h0: %.2f\n', best_params.h0);
    fprintf('  초기 h1: %.2f\n', best_params.h1);
    fprintf('  동기 임계값: %.2f\n', best_params.sync_ratio);

    % 최적 파라미터로 최종 테스트
    fprintf('\n=== 최적 파라미터로 최종 테스트 ===\n');
    final_packet_count = run_viterbi_test_with_params(best_params, G_pFilteredData, G_vGmskPreamble, sync_pattern);
    fprintf('최종 패킷 수: %d\n', final_packet_count);

    % 결과 분석 및 저장
    if ENABLE_FILE_SAVE
        save_optimization_results(optimization_results, best_params, final_packet_count);
    end

    % 성능 분석 플롯
    if ENABLE_FREQ_ANALYSIS
        plot_optimization_results(optimization_results);
    end

else
    % 기존 단일 테스트 실행
    fprintf('\n=== 단일 파라미터 테스트 ===\n');

%-------------------------------------------------------------------------
% 메인 처리 루프 - Viterbi MLSD 기반 패킷 검출
%-------------------------------------------------------------------------

% 상관 검출을 통한 프리앰블 검색
fprintf('\n프리앰블 검색 중...\n');

if (ENABLE_GMSK_RX_FLT > 0)
    correlation = conv(G_pFilteredData, flipud(G_vGmskPreamble), 'same');
else
    correlation = conv(G_pSrcDataCh1, flipud(G_vGmskPreamble), 'same');
end

% 피크 검출
correlation_abs = abs(correlation);
correlation_normalized = correlation_abs / max(correlation_abs);

% 적응형 임계값 설정 (더 낮은 임계값으로 더 많은 피크 검출)
noise_floor = median(correlation_normalized);
adaptive_threshold = max(0.3, noise_floor + 0.1);  % 임계값 대폭 낮춤

% 피크 찾기 (더 많은 피크 검출을 위해 거리 제한 완화)
[peaks, peak_indices] = findpeaks(correlation_normalized, 'MinPeakHeight', adaptive_threshold, ...
                                  'MinPeakDistance', length(G_vGmskPreamble)/4);  % 거리 제한 완화

fprintf('검출된 피크 수: %d (임계값: %.3f)\n', length(peaks), adaptive_threshold);

% 검출된 각 피크에 대해 Viterbi MLSD 처리
total_packets = 0;
successful_packets = 0;

for peak_idx = 1:length(peak_indices)
    sync_index = peak_indices(peak_idx);

    fprintf('\n--- 피크 %d/%d 처리 중 (위치: %d) ---\n', peak_idx, length(peak_indices), sync_index);

    % 채널 추정을 위한 프리앰블 신호 추출
    if ENABLE_CHANNEL_ESTIMATION
        preamble_start = max(1, sync_index - length(G_vGmskPreamble) + 1);
        preamble_end = min(length(G_pFilteredData), sync_index);

        if preamble_end > preamble_start
            if (ENABLE_GMSK_RX_FLT > 0)
                received_preamble = G_pFilteredData(preamble_start:preamble_end);
            else
                received_preamble = G_pSrcDataCh1(preamble_start:preamble_end);
            end

            % 고급 채널 추정 수행
            [new_h0, new_h1, new_bias, channel_confidence] = estimate_channel_v3(...
                received_preamble, preamble_bits, G_ChannelH0, G_ChannelH1, G_ChannelBias);

            % 채널 계수 업데이트 (신뢰도 기반)
            if channel_confidence > 0.1
                alpha = CHANNEL_UPDATE_RATE * channel_confidence;
                G_ChannelH0 = (1-alpha) * G_ChannelH0 + alpha * new_h0;
                G_ChannelH1 = (1-alpha) * G_ChannelH1 + alpha * new_h1;
                G_ChannelBias = (1-alpha) * G_ChannelBias + alpha * new_bias;

                G_ViterbiStats.channel_estimations = G_ViterbiStats.channel_estimations + 1;

                % 채널 히스토리 저장
                G_ChannelHistory.h0 = [G_ChannelHistory.h0, G_ChannelH0];
                G_ChannelHistory.h1 = [G_ChannelHistory.h1, G_ChannelH1];
                G_ChannelHistory.bias = [G_ChannelHistory.bias, G_ChannelBias];
                G_ChannelHistory.confidence = [G_ChannelHistory.confidence, channel_confidence];

                fprintf('채널 추정: h0=%.4f, h1=%.4f, bias=%.4f (신뢰도: %.3f)\n', ...
                    G_ChannelH0, G_ChannelH1, G_ChannelBias, channel_confidence);
            end
        end
    end

    % 적응형 노이즈 분산 추정
    if VITERBI_ADAPTIVE_NOISE
        data_start = max(1, sync_index - 50);
        data_end = min(length(G_pFilteredData), sync_index + 50);
        if (ENABLE_GMSK_RX_FLT > 0)
            noise_data = G_pFilteredData(data_start:data_end);
        else
            noise_data = G_pSrcDataCh1(data_start:data_end);
        end
        G_NoiseVariance = estimate_noise_variance_adaptive(noise_data, 32);
    end

    % 패킷 데이터 추출 (프리앰블 이후)
    packet_start = sync_index + 1;
    packet_end = min(length(G_pFilteredData), packet_start + VITERBI_WINDOW_SIZE - 1);

    if packet_end > packet_start
        if (ENABLE_GMSK_RX_FLT > 0)
            packet_signal = G_pFilteredData(packet_start:packet_end);
        else
            packet_signal = G_pSrcDataCh1(packet_start:packet_end);
        end

        % Viterbi MLSD 수행
        [detected_bits, path_metrics, viterbi_confidence] = viterbi_mlsd_v3(...
            packet_signal, G_ChannelH0, G_ChannelH1, G_ChannelBias, ...
            VITERBI_WINDOW_SIZE, VITERBI_TRACEBACK_DEPTH, G_NoiseVariance);

        total_packets = total_packets + 1;

        if ~isempty(detected_bits) && isscalar(viterbi_confidence) && viterbi_confidence > VITERBI_CONFIDENCE_THRESHOLD
            % 동기 패턴 검증
            if length(detected_bits) >= length(sync_pattern)
                sync_match = sum(detected_bits(1:length(sync_pattern)) == sync_pattern);
                sync_ratio = sync_match / length(sync_pattern);

                if sync_ratio >= SYNC_RATIO_THRESHOLD  % 동적 임계값
                    successful_packets = successful_packets + 1;

                    % 신뢰도 히스토리 업데이트
                    G_ViterbiStats.confidence_history = [G_ViterbiStats.confidence_history, viterbi_confidence];

                    % AIS 패킷 길이 검증
                    packet_length = length(detected_bits);
                    is_valid_length = (packet_length >= PACKET_MIN_LENGTH && packet_length <= PACKET_MAX_LENGTH);

                    fprintf('패킷 검출 성공! 비트 수: %d, 신뢰도: %.3f, 동기 일치율: %.1f%%\n', ...
                        packet_length, viterbi_confidence, sync_ratio*100);

                    if is_valid_length
                        fprintf('✓ AIS 패킷 길이 유효 (%d비트)\n', packet_length);
                    else
                        fprintf('⚠ AIS 패킷 길이 부족 (%d비트, 최소 %d비트 필요)\n', packet_length, PACKET_MIN_LENGTH);
                    end

                    % 패킷 유효성 추가 검사
                    if validate_ais_packet(detected_bits)
                        fprintf('✓ AIS 패킷 유효성 검증 통과\n');
                    end
                else
                    fprintf('동기 패턴 불일치 (일치율: %.1f%%)\n', sync_ratio*100);
                end
            else
                fprintf('검출된 비트 수 부족: %d\n', length(detected_bits));
            end
        else
            if isempty(detected_bits)
                fprintf('Viterbi 검출 실패\n');
            else
                fprintf('신뢰도 부족: %.3f (임계값: %.3f)\n', viterbi_confidence, VITERBI_CONFIDENCE_THRESHOLD);
            end
        end
    end
end

% 통계 업데이트
G_ViterbiStats.total_packets = total_packets;
G_ViterbiStats.successful_packets = successful_packets;

if ~isempty(G_ViterbiStats.confidence_history)
    G_ViterbiStats.avg_confidence = mean(G_ViterbiStats.confidence_history);
end

%-------------------------------------------------------------------------
% 결과 출력
%-------------------------------------------------------------------------
fprintf('\n=== 처리 결과 ===\n');
fprintf('총 검출 시도: %d\n', total_packets);
fprintf('성공적 패킷: %d\n', successful_packets);
if total_packets > 0
    success_rate = successful_packets / total_packets * 100;
    fprintf('성공률: %.1f%%\n', success_rate);
end

fprintf('\n=== Viterbi MLSD 성능 ===\n');
fprintf('채널 추정 횟수: %d\n', G_ViterbiStats.channel_estimations);
fprintf('최종 채널 계수: h0=%.4f, h1=%.4f, bias=%.4f\n', G_ChannelH0, G_ChannelH1, G_ChannelBias);
fprintf('노이즈 분산: %.6f\n', G_NoiseVariance);

if ~isempty(G_ViterbiStats.confidence_history)
    fprintf('평균 신뢰도: %.3f\n', G_ViterbiStats.avg_confidence);
    fprintf('신뢰도 범위: %.3f ~ %.3f\n', min(G_ViterbiStats.confidence_history), max(G_ViterbiStats.confidence_history));
end

% 채널 추적 결과
if CHANNEL_TRACKING_ENABLE && length(G_ChannelHistory.h0) > 1
    fprintf('\n=== 채널 추적 결과 ===\n');
    fprintf('h0 변화: %.4f → %.4f\n', G_ChannelHistory.h0(1), G_ChannelHistory.h0(end));
    fprintf('h1 변화: %.4f → %.4f\n', G_ChannelHistory.h1(1), G_ChannelHistory.h1(end));
    fprintf('bias 변화: %.4f → %.4f\n', G_ChannelHistory.bias(1), G_ChannelHistory.bias(end));
end

fprintf('\n=== AIS 수신기 v3 처리 완료 ===\n');
fprintf('최종 패킷 수: %d\n', successful_packets);

%-------------------------------------------------------------------------
% 시각화 및 분석 (디버그 모드)
%-------------------------------------------------------------------------
if ENABLE_DEBUG || ENABLE_FREQ_ANALYSIS
    figure('Name', 'AIS 수신기 v3 - Viterbi MLSD 분석', 'Position', [100, 100, 1200, 800]);

    % 1. 상관 검출 결과
    if ENABLE_PLOT1
        subplot(3, 2, 1);
        plot(correlation_normalized);
        hold on;
        plot(peak_indices, peaks, 'ro', 'MarkerSize', 8);
        yline(adaptive_threshold, 'r--', 'LineWidth', 2);
        title('프리앰블 상관 검출');
        xlabel('샘플 인덱스');
        ylabel('정규화된 상관값');
        legend('상관값', '검출된 피크', '적응형 임계값');
        grid on;
    end

    % 2. 채널 계수 변화
    if ENABLE_PLOT2 && length(G_ChannelHistory.h0) > 1
        subplot(3, 2, 2);
        plot(G_ChannelHistory.h0, 'b-o', 'LineWidth', 2);
        hold on;
        plot(G_ChannelHistory.h1, 'r-s', 'LineWidth', 2);
        plot(G_ChannelHistory.bias, 'g-^', 'LineWidth', 2);
        title('채널 계수 추적');
        xlabel('추정 횟수');
        ylabel('계수 값');
        legend('h0', 'h1', 'bias');
        grid on;
    end

    % 3. 신뢰도 히스토리
    if ENABLE_PLOT3 && ~isempty(G_ViterbiStats.confidence_history)
        subplot(3, 2, 3);
        plot(G_ViterbiStats.confidence_history, 'b-o', 'LineWidth', 2);
        hold on;
        yline(VITERBI_CONFIDENCE_THRESHOLD, 'r--', 'LineWidth', 2);
        title('Viterbi 신뢰도 변화');
        xlabel('패킷 번호');
        ylabel('신뢰도');
        legend('신뢰도', '임계값');
        grid on;
    end

    % 4. 신호 스펙트럼 분석
    if ENABLE_PLOT4 && ENABLE_FREQ_ANALYSIS
        subplot(3, 2, 4);
        if (ENABLE_GMSK_RX_FLT > 0)
            [psd, freq] = pwelch(G_pFilteredData, [], [], [], BIT_RATE*OSR);
        else
            [psd, freq] = pwelch(G_pSrcDataCh1, [], [], [], BIT_RATE*OSR);
        end
        semilogy(freq, psd);
        title('신호 전력 스펙트럼 밀도');
        xlabel('주파수 (Hz)');
        ylabel('PSD');
        grid on;
    end

    % 5. 채널 추정 신뢰도
    if ENABLE_PLOT5 && ~isempty(G_ChannelHistory.confidence)
        subplot(3, 2, 5);
        plot(G_ChannelHistory.confidence, 'g-o', 'LineWidth', 2);
        title('채널 추정 신뢰도');
        xlabel('추정 횟수');
        ylabel('신뢰도');
        grid on;
    end

    % 6. 성능 요약
    if ENABLE_PLOT6
        subplot(3, 2, 6);
        categories = {'총 시도', '성공', '실패'};
        values = [total_packets, successful_packets, total_packets - successful_packets];
        bar(values);
        set(gca, 'XTickLabel', categories);
        title('패킷 검출 성능 요약');
        ylabel('패킷 수');
        for i = 1:length(values)
            text(i, values(i) + max(values)*0.02, num2str(values(i)), ...
                'HorizontalAlignment', 'center', 'FontWeight', 'bold');
        end
        grid on;
    end

    sgtitle('AIS 수신기 v3 - Viterbi MLSD 성능 분석');
end

%-------------------------------------------------------------------------
% 성능 비교 및 권장사항
%-------------------------------------------------------------------------
fprintf('\n=== 성능 분석 및 권장사항 ===\n');

if successful_packets >= 170
    fprintf('✓ 목표 성능 달성! (170+ 패킷)\n');
elseif successful_packets >= 150
    fprintf('△ 양호한 성능 (150+ 패킷)\n');
    fprintf('권장사항: VITERBI_WINDOW_SIZE 또는 TRACEBACK_DEPTH 증가 고려\n');
else
    fprintf('✗ 성능 개선 필요\n');
    fprintf('권장사항:\n');
    fprintf('  1. CHANNEL_UPDATE_RATE 조정 (현재: %.2f)\n', CHANNEL_UPDATE_RATE);
    fprintf('  2. VITERBI_CONFIDENCE_THRESHOLD 낮추기 (현재: %.2f)\n', VITERBI_CONFIDENCE_THRESHOLD);
    fprintf('  3. 노이즈 환경 개선 필요\n');
end

if G_ViterbiStats.channel_estimations > 0
    estimation_rate = G_ViterbiStats.channel_estimations / total_packets;
    if estimation_rate < 0.5
        fprintf('주의: 채널 추정 성공률 낮음 (%.1f%%)\n', estimation_rate*100);
    end
end

if ~isempty(G_ViterbiStats.confidence_history)
    low_confidence_count = sum(G_ViterbiStats.confidence_history < VITERBI_CONFIDENCE_THRESHOLD);
    if low_confidence_count > successful_packets * 0.3
        fprintf('주의: 낮은 신뢰도 검출이 많음 (%d개)\n', low_confidence_count);
    end
end

%-------------------------------------------------------------------------
% 파일 저장 (옵션)
%-------------------------------------------------------------------------
if ENABLE_FILE_SAVE
    % 결과 저장
    results = struct();
    results.total_packets = total_packets;
    results.successful_packets = successful_packets;
    results.viterbi_stats = G_ViterbiStats;
    results.channel_history = G_ChannelHistory;
    results.final_channel_coeffs = [G_ChannelH0, G_ChannelH1, G_ChannelBias];
    results.noise_variance = G_NoiseVariance;

    save('ais_receiver_v3_results.mat', 'results');
    fprintf('\n결과가 ais_receiver_v3_results.mat에 저장되었습니다.\n');
end

fprintf('\n프로그램 종료.\n');

end  % ENABLE_AUTO_OPTIMIZATION if문 종료

%-------------------------------------------------------------------------
% 자동 최적화 지원 함수들
%-------------------------------------------------------------------------

%------------------------------------------------------------------------
% 스마트 파라미터 조합 생성
function param_combinations = generate_smart_parameter_combinations(window_range, traceback_range, confidence_range, ...
    channel_range, h0_range, h1_range, sync_range)
    % 효율적인 파라미터 탐색을 위한 스마트 조합 생성

    % 우선순위 기반 조합 생성
    combinations = [];

    % 1단계: 기본 조합 (빠른 탐색)
    for i = 1:3:length(window_range)
        for j = 1:2:length(traceback_range)
            for k = 1:2:length(confidence_range)
                combo = struct();
                combo.window_size = window_range(i);
                combo.traceback_depth = traceback_range(j);
                combo.confidence_threshold = confidence_range(k);
                combo.channel_update_rate = channel_range(min(3, length(channel_range))); % 중간값
                combo.h0 = h0_range(min(4, length(h0_range))); % 중간값
                combo.h1 = h1_range(min(4, length(h1_range))); % 중간값
                combo.sync_ratio = sync_range(min(3, length(sync_range))); % 중간값
                combinations = [combinations; combo];
            end
        end
    end

    % 2단계: 세밀한 조합 (성능 개선)
    for i = 1:length(channel_range)
        for j = 1:length(h0_range)
            for k = 1:length(h1_range)
                combo = struct();
                combo.window_size = window_range(min(3, length(window_range))); % 중간값
                combo.traceback_depth = traceback_range(min(3, length(traceback_range))); % 중간값
                combo.confidence_threshold = confidence_range(min(3, length(confidence_range))); % 중간값
                combo.channel_update_rate = channel_range(i);
                combo.h0 = h0_range(j);
                combo.h1 = h1_range(k);
                combo.sync_ratio = sync_range(min(3, length(sync_range))); % 중간값
                combinations = [combinations; combo];
            end
        end
    end

    % 3단계: 극값 조합 (극한 성능 탐색)
    extreme_combinations = [
        struct('window_size', max(window_range), 'traceback_depth', max(traceback_range), ...
               'confidence_threshold', min(confidence_range), 'channel_update_rate', max(channel_range), ...
               'h0', max(h0_range), 'h1', min(h1_range), 'sync_ratio', min(sync_range));
        struct('window_size', min(window_range), 'traceback_depth', min(traceback_range), ...
               'confidence_threshold', max(confidence_range), 'channel_update_rate', min(channel_range), ...
               'h0', min(h0_range), 'h1', max(h1_range), 'sync_ratio', max(sync_range));
    ];

    combinations = [combinations; extreme_combinations];

    % 중복 제거 및 셔플
    param_combinations = unique_combinations(combinations);
    param_combinations = param_combinations(randperm(length(param_combinations)));
end

%------------------------------------------------------------------------
% 파라미터로 Viterbi 테스트 실행
function packet_count = run_viterbi_test_with_params(params, filtered_data, gmsk_preamble, sync_pattern)
    % 주어진 파라미터로 Viterbi MLSD 테스트 실행

    try
        % 상관 검출
        correlation = conv(filtered_data, flipud(gmsk_preamble), 'same');
        correlation_abs = abs(correlation);
        correlation_normalized = correlation_abs / max(correlation_abs);

        % 적응형 임계값
        noise_floor = median(correlation_normalized);
        adaptive_threshold = max(0.75, noise_floor + 0.3);

        % 피크 검출
        [~, peak_indices] = findpeaks(correlation_normalized, 'MinPeakHeight', adaptive_threshold, ...
                                      'MinPeakDistance', length(gmsk_preamble));

        successful_packets = 0;

        % 각 피크에 대해 Viterbi 처리
        for peak_idx = 1:length(peak_indices)
            sync_index = peak_indices(peak_idx);

            % 패킷 데이터 추출
            packet_start = sync_index + 1;
            packet_end = min(length(filtered_data), packet_start + params.window_size - 1);

            if packet_end > packet_start
                packet_signal = filtered_data(packet_start:packet_end);

                % Viterbi MLSD 수행
                [detected_bits, ~, viterbi_confidence] = viterbi_mlsd_v3(...
                    packet_signal, params.h0, params.h1, 0.0, ...
                    params.window_size, params.traceback_depth, 0.005);

                % 검증
                if ~isempty(detected_bits) && isscalar(viterbi_confidence) && ...
                   viterbi_confidence > params.confidence_threshold

                    % 동기 패턴 검증
                    if length(detected_bits) >= length(sync_pattern)
                        sync_match = sum(detected_bits(1:length(sync_pattern)) == sync_pattern);
                        sync_ratio = sync_match / length(sync_pattern);

                        if sync_ratio >= params.sync_ratio
                            successful_packets = successful_packets + 1;
                        end
                    end
                end
            end
        end

        packet_count = successful_packets;

    catch ME
        fprintf('테스트 중 오류 발생: %s\n', ME.message);
        packet_count = 0;
    end
end

%------------------------------------------------------------------------
% 중복 조합 제거
function unique_combos = unique_combinations(combinations)
    % 구조체 배열에서 중복 제거

    if isempty(combinations)
        unique_combos = combinations;
        return;
    end

    % 간단한 중복 제거 (필드 값 기반)
    unique_combos = combinations(1);

    for i = 2:length(combinations)
        is_duplicate = false;
        for j = 1:length(unique_combos)
            if isequal_struct(combinations(i), unique_combos(j))
                is_duplicate = true;
                break;
            end
        end
        if ~is_duplicate
            unique_combos = [unique_combos; combinations(i)];
        end
    end
end

%------------------------------------------------------------------------
% 구조체 동등성 검사
function is_equal = isequal_struct(s1, s2)
    % 두 구조체가 동일한지 검사

    fields1 = fieldnames(s1);
    fields2 = fieldnames(s2);

    if ~isequal(fields1, fields2)
        is_equal = false;
        return;
    end

    is_equal = true;
    for i = 1:length(fields1)
        if ~isequal(s1.(fields1{i}), s2.(fields1{i}))
            is_equal = false;
            return;
        end
    end
end

%------------------------------------------------------------------------
% 최적화 결과 저장
function save_optimization_results(results, best_params, final_count)
    % 최적화 결과를 파일로 저장

    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    filename = sprintf('viterbi_optimization_results_%s.mat', timestamp);

    optimization_data = struct();
    optimization_data.results = results;
    optimization_data.best_params = best_params;
    optimization_data.final_packet_count = final_count;
    optimization_data.timestamp = timestamp;

    save(filename, 'optimization_data');
    fprintf('최적화 결과가 %s에 저장되었습니다.\n', filename);
end

%------------------------------------------------------------------------
% 최적화 결과 플롯
function plot_optimization_results(results)
    % 최적화 결과 시각화

    if isempty(results)
        return;
    end

    figure('Name', 'Viterbi MLSD 자동 최적화 결과', 'Position', [100, 100, 1200, 800]);

    % 패킷 수 vs 반복 횟수
    subplot(2, 3, 1);
    packet_counts = [results.packet_count];
    plot(1:length(packet_counts), packet_counts, 'b-o', 'LineWidth', 2);
    title('패킷 수 vs 반복 횟수');
    xlabel('반복 횟수');
    ylabel('검출된 패킷 수');
    grid on;

    % 윈도우 크기 vs 패킷 수
    subplot(2, 3, 2);
    window_sizes = [results.window_size];
    scatter(window_sizes, packet_counts, 50, 'filled');
    title('윈도우 크기 vs 패킷 수');
    xlabel('윈도우 크기');
    ylabel('검출된 패킷 수');
    grid on;

    % 신뢰도 임계값 vs 패킷 수
    subplot(2, 3, 3);
    confidence_thresholds = [results.confidence_threshold];
    scatter(confidence_thresholds, packet_counts, 50, 'filled');
    title('신뢰도 임계값 vs 패킷 수');
    xlabel('신뢰도 임계값');
    ylabel('검출된 패킷 수');
    grid on;

    % h0 vs 패킷 수
    subplot(2, 3, 4);
    h0_values = [results.h0];
    scatter(h0_values, packet_counts, 50, 'filled');
    title('초기 h0 vs 패킷 수');
    xlabel('초기 h0 값');
    ylabel('검출된 패킷 수');
    grid on;

    % h1 vs 패킷 수
    subplot(2, 3, 5);
    h1_values = [results.h1];
    scatter(h1_values, packet_counts, 50, 'filled');
    title('초기 h1 vs 패킷 수');
    xlabel('초기 h1 값');
    ylabel('검출된 패킷 수');
    grid on;

    % 최고 성능 히스토그램
    subplot(2, 3, 6);
    histogram(packet_counts, 20);
    title('패킷 수 분포');
    xlabel('검출된 패킷 수');
    ylabel('빈도');
    grid on;

    sgtitle('Viterbi MLSD 자동 최적화 분석');
end
