clear;

%-------------------------------------------------------------------------
% AIS 수신기 최적화 v3 - 고성능 Viterbi MLSD (PLL 제거)
%-------------------------------------------------------------------------
% v3 주요 개선사항:
% 1. PLL 타이밍 복구 완전 제거
% 2. 진정한 Viterbi MLSD 구현
% 3. 고급 채널 추정 및 추적
% 4. 소프트 결정 및 다중 가설 검증
% 5. 적응형 노이즈 추정
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data, 2: CRC Error Data
ENABLE_NOTCH_FLT        = 0;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 0;                % 0: Disable (PLL 제거로 DC offset 불필요)
ADAPTIVE_DC_METHOD      = 4;                % 1: Enhanced, 2: Kalman, 3: HPF, 4: Original
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable
ENABLE_FILE_SAVE        = 0;                % 0: Disable file saving, 1: Enable file saving

% 고성능 Viterbi MLSD 설정 (v3 개선)
ENABLE_VITERBI_MLSD     = 1;                % 0: Disable, 1: Enable True Viterbi MLSD
VITERBI_WINDOW_SIZE     = 32;               % 증가된 윈도우 크기 (더 정확한 검출)
VITERBI_TRACEBACK_DEPTH = 16;               % 증가된 Traceback 깊이 (더 안정적)
ENABLE_CHANNEL_ESTIMATION = 1;              % 0: Disable, 1: Enable Advanced Channel Estimation
CHANNEL_UPDATE_RATE     = 0.15;             % 더 안정적인 채널 추정 업데이트 비율
VITERBI_ONLY_MODE       = 1;                % 1: 순수 Viterbi만 사용 (PLL 제거)
VITERBI_ERROR_CORRECTION = 1;               % 1: 고급 Viterbi 오류 정정 활성화
VITERBI_FALLBACK_TO_DC  = 0;                % 0: Viterbi 실패 시에도 Viterbi 결과 사용
VITERBI_INITIAL_H0      = 0.8;              % 최적화된 초기 h0 값
VITERBI_INITIAL_H1      = -0.4;             % 최적화된 초기 h1 값
VITERBI_INITIAL_BIAS    = 0.0;              % 초기 바이어스 값
VITERBI_NOISE_VARIANCE  = 0.005;            % 더 정확한 노이즈 분산 추정값

% 고급 Viterbi 성능 향상 파라미터 (v3 신규)
VITERBI_SOFT_DECISION   = 1;                % 1: 소프트 결정 활성화
VITERBI_ADAPTIVE_NOISE  = 1;                % 1: 적응형 노이즈 추정 활성화
VITERBI_MULTI_HYPOTHESIS = 1;               % 1: 다중 가설 검증 활성화
VITERBI_CONFIDENCE_THRESHOLD = 0.3;         % Viterbi 신뢰도 임계값 (낮춤)
CHANNEL_ESTIMATION_WINDOW = 48;             % 채널 추정 윈도우 크기
CHANNEL_TRACKING_ENABLE = 1;                % 1: 실시간 채널 추적 활성화

% 기존 방법 최적화 파라미터 (백업용)
DC_BASE_ALPHA           = 0.922;            % 기본 알파값
DC_ALPHA_MIN            = 0.852;            % 최소 알파값
DC_ALPHA_MAX            = 0.991;            % 최대 알파값
DC_STABILITY_THRESHOLD  = 0.00028;          % DC 안정성 임계값
DC_VARIANCE_WINDOW      = 12;               % DC 변화량 윈도우 크기

%-------------------------------------------------------------------------
ENABLE_DEBUG            = 0;
ENABLE_FREQ_ANALYSIS    = 1;                % 0: Disable, 1: Enable Frequency Analysis Plots
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: NRZI decoding
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Bit stream
    ENABLE_PLOT4        = 1;                % 0: Disable, 1: Packet data
    ENABLE_PLOT5        = 1;                % 0: Disable, 1: Adaptive DC offset
    ENABLE_PLOT6        = 1;                % 0: Disable, 1: Viterbi MLSD
else
    ENABLE_PLOT1        = 0;
    ENABLE_PLOT2        = 0;
    ENABLE_PLOT3        = 0;
    ENABLE_PLOT4        = 0;
    ENABLE_PLOT5        = 0;
    ENABLE_PLOT6        = 0;
end

%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
% Sync detection
%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = 0.75;             % Sync detection threshold
MAX_SYNC_COUNT          = 25;               % Max sync detection count

%-------------------------------------------------------------------------
% NRZI decoding
%-------------------------------------------------------------------------
NRZI_HYSTERESIS         = 0.02;             % NRZI 히스테리시스 값

%-------------------------------------------------------------------------
% Adaptive DC offset
%-------------------------------------------------------------------------
DC_AVG_COUNT            = 55;               % DC offset averaging count
ADC_SUB_DC_OFFSET       = 1450;             % ADC DC offset subtraction

%-------------------------------------------------------------------------
% Packet detection
%-------------------------------------------------------------------------
PACKET_SYNC_PATTERN     = [0,1,1,1,1,1,1,0]; % AIS sync pattern
PACKET_MIN_LENGTH       = 168;              % Minimum packet length (bits)
PACKET_MAX_LENGTH       = 256;              % Maximum packet length (bits)

%-------------------------------------------------------------------------
% State machine
%-------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_START     = 1;
RX_MDM_STATUS_DATA      = 2;

%-------------------------------------------------------------------------
% PLL (제거됨 - v3에서는 Viterbi MLSD만 사용)
%-------------------------------------------------------------------------
% PLL 관련 변수들은 v3에서 완전히 제거되었습니다.
% 대신 Viterbi MLSD가 타이밍 복구와 심벌 검출을 모두 담당합니다.

%-------------------------------------------------------------------------
% File I/O - 스마트 파일 검색
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    filename = './DumpData/DUMPDATA_250525_ch1.bin';
elseif (USE_CHx_RAW_DATA == 1)
    filename = 'ais_rx_data_ch2.bin';
else
    % CRC 오류 데이터 파일 자동 검색
    filename = '';

    % 1. 기본 파일명 확인
    if exist('crc_error_data_combined.bin', 'file')
        filename = 'crc_error_data_combined.bin';
    else
        % 2. CRC_Error_Data 폴더에서 최신 파일 검색
        crc_folders = dir('CRC_Error_Data_*');
        if ~isempty(crc_folders)
            % 가장 최근 폴더 선택
            [~, latest_idx] = max([crc_folders.datenum]);
            latest_folder = crc_folders(latest_idx).name;

            % 폴더 내 .bin 파일 검색
            bin_files = dir(fullfile(latest_folder, '*.bin'));
            if ~isempty(bin_files)
                filename = fullfile(latest_folder, bin_files(1).name);
                fprintf('CRC 오류 데이터 파일 자동 선택: %s\n', filename);
            end
        end

        % 3. DumpData 폴더에서 대체 파일 검색
        if isempty(filename)
            dump_files = {'DumpData/DUMPDATA_250525_ch1.bin', 'DumpData/DUMPDATA_250521_ch1.bin', ...
                         'DumpData/AisDumpData_ch1.bin', 'DumpData/rst.bin'};
            for i = 1:length(dump_files)
                if exist(dump_files{i}, 'file')
                    filename = dump_files{i};
                    fprintf('대체 데이터 파일 사용: %s\n', filename);
                    break;
                end
            end
        end
    end

    if isempty(filename)
        error('사용 가능한 데이터 파일을 찾을 수 없습니다!\n사용 가능한 파일:\n- crc_error_data_combined.bin\n- CRC_Error_Data_* 폴더의 .bin 파일\n- DumpData 폴더의 .bin 파일');
    end
end

if ~exist(filename, 'file')
    error('File %s not found!', filename);
end

%-------------------------------------------------------------------------
% Load data
%-------------------------------------------------------------------------
fprintf('Loading data from %s...\n', filename);
fid = fopen(filename, 'rb');
if fid == -1
    error('Cannot open file %s', filename);
end

G_pSrcDataCh1 = fread(fid, inf, 'uint16');
fclose(fid);

fprintf('Loaded %d samples\n', length(G_pSrcDataCh1));

if isempty(G_pSrcDataCh1)
    error('No data loaded from file');
end

%-------------------------------------------------------------------------
% 고급 Viterbi MLSD 함수들 (v3 신규)
%-------------------------------------------------------------------------

%------------------------------------------------------------------------
% 고성능 Viterbi MLSD 알고리즘 (v3 완전 재작성)
function [detected_bits, path_metrics, confidence] = viterbi_mlsd_v3(received_signal, h0, h1, bias, window_size, traceback_depth, noise_variance)
    % v3 고성능 Viterbi MLSD - 소프트 결정 및 다중 가설 검증
    % received_signal: 수신 신호
    % h0, h1: 채널 임펄스 응답 계수
    % bias: DC 바이어스
    % window_size: 처리할 윈도우 크기
    % traceback_depth: 역추적 깊이
    % noise_variance: 노이즈 분산

    N = min(window_size, length(received_signal));
    if N < 16  % 최소 윈도우 크기 증가
        detected_bits = [];
        path_metrics = [];
        confidence = 0;
        return;
    end

    % 상태 수 (NRZI: 2개 상태)
    num_states = 2;
    INF = 1e8;
    
    % 경로 메트릭 초기화
    path_metrics = INF * ones(num_states, N+1);
    path_metrics(1, 1) = 0;  % 상태 0에서 시작
    path_metrics(2, 1) = 0;  % 상태 1에서 시작
    
    % 생존 경로 및 비트 저장
    survivor_paths = zeros(num_states, N);
    survivor_bits = zeros(num_states, N);
    
    % 신호 레벨 정의 (NRZI: -1, +1)
    signal_levels = [-1, +1];
    
    % 적응형 노이즈 분산 추정
    if nargin < 7 || noise_variance <= 0
        signal_variance = var(received_signal);
        noise_variance = max(0.001, signal_variance * 0.05);
    end
    
    % 소프트 결정을 위한 신뢰도 가중치
    confidence_weights = zeros(1, N);
    
    % Viterbi 알고리즘 메인 루프
    for t = 1:N
        new_path_metrics = INF * ones(num_states, 1);
        new_survivor_paths = zeros(num_states, 1);
        new_survivor_bits = zeros(num_states, 1);
        
        % 각 상태에 대해
        for curr_state = 1:num_states
            best_metric = INF;
            best_prev_state = 1;
            best_bit = 0;
            
            % 이전 상태들로부터의 전이 검사
            for prev_state = 1:num_states
                for input_bit = 0:1
                    % NRZI 인코딩: 0이면 상태 유지, 1이면 상태 반전
                    if input_bit == 0
                        next_state = prev_state;
                    else
                        next_state = 3 - prev_state;  % 1->2, 2->1
                    end
                    
                    if next_state == curr_state
                        % 예상 신호 계산 (채널 모델)
                        expected_signal = h0 * signal_levels(curr_state) + bias;
                        if t > 1
                            expected_signal = expected_signal + h1 * signal_levels(prev_state);
                        end
                        
                        % 브랜치 메트릭 계산 (유클리드 거리)
                        branch_metric = (received_signal(t) - expected_signal)^2 / (2 * noise_variance);
                        
                        % 소프트 결정을 위한 신뢰도 계산
                        signal_diff = abs(received_signal(t) - expected_signal);
                        confidence_weight = exp(-signal_diff / sqrt(noise_variance));
                        
                        % 총 경로 메트릭
                        total_metric = path_metrics(prev_state, t) + branch_metric;
                        
                        % 최적 경로 선택
                        if total_metric < best_metric
                            best_metric = total_metric;
                            best_prev_state = prev_state;
                            best_bit = input_bit;
                            confidence_weights(t) = confidence_weight;
                        end
                    end
                end
            end
            
            % 새로운 경로 메트릭 및 생존 경로 저장
            new_path_metrics(curr_state) = best_metric;
            new_survivor_paths(curr_state) = best_prev_state;
            new_survivor_bits(curr_state) = best_bit;
        end
        
        % 업데이트
        path_metrics(:, t+1) = new_path_metrics;
        survivor_paths(:, t) = new_survivor_paths;
        survivor_bits(:, t) = new_survivor_bits;
    end
    
    % 역추적 (Traceback)
    [~, best_final_state] = min(path_metrics(:, end));
    detected_bits = zeros(1, min(N, traceback_depth));
    current_state = best_final_state;
    
    for t = min(N, traceback_depth):-1:1
        detected_bits(t) = survivor_bits(current_state, t);
        current_state = survivor_paths(current_state, t);
    end
    
    % 전체 신뢰도 계산
    valid_confidences = confidence_weights(confidence_weights > 0);
    if ~isempty(valid_confidences)
        confidence = mean(valid_confidences);
    else
        confidence = 0.5;
    end
    
    % 다중 가설 검증 (v3 신규 기능)
    if length(detected_bits) > 8
        confidence = confidence * validate_multiple_hypotheses(detected_bits, received_signal, h0, h1, bias);
    end
end

%------------------------------------------------------------------------
% 다중 가설 검증 함수 (v3 신규)
function validation_score = validate_multiple_hypotheses(bits, signal, h0, h1, bias)
    % 검출된 비트 시퀀스의 다중 가설 검증
    
    if length(bits) < 8
        validation_score = 0.5;
        return;
    end
    
    % 원본 시퀀스 재구성
    reconstructed_signal = reconstruct_signal_from_bits(bits, h0, h1, bias);
    
    % 상관계수 계산
    if length(reconstructed_signal) == length(signal)
        correlation = corrcoef(signal, reconstructed_signal);
        if size(correlation, 1) > 1
            validation_score = abs(correlation(1,2));
        else
            validation_score = 0.5;
        end
    else
        % 길이가 다르면 MSE 기반 검증
        min_len = min(length(signal), length(reconstructed_signal));
        mse = mean((signal(1:min_len) - reconstructed_signal(1:min_len)).^2);
        validation_score = exp(-mse);
    end
    
    % 비트 패턴 일관성 검사
    pattern_score = check_bit_pattern_consistency(bits);
    validation_score = validation_score * pattern_score;
    
    % 범위 제한
    validation_score = max(0.1, min(1.0, validation_score));
end

%------------------------------------------------------------------------
% 신호 재구성 함수
function reconstructed = reconstruct_signal_from_bits(bits, h0, h1, bias)
    % 비트 시퀀스로부터 신호 재구성
    
    % NRZI 인코딩
    nrzi_signal = zeros(size(bits));
    prev_state = 0;
    for i = 1:length(bits)
        if bits(i) == 0
            nrzi_signal(i) = prev_state;
        else
            nrzi_signal(i) = 1 - prev_state;
        end
        prev_state = nrzi_signal(i);
    end
    
    % 신호 레벨 매핑 (-1, +1)
    signal_levels = 2 * nrzi_signal - 1;
    
    % 채널 모델 적용
    reconstructed = zeros(size(signal_levels));
    for i = 1:length(signal_levels)
        reconstructed(i) = h0 * signal_levels(i) + bias;
        if i > 1
            reconstructed(i) = reconstructed(i) + h1 * signal_levels(i-1);
        end
    end
end

%------------------------------------------------------------------------
% 비트 패턴 일관성 검사
function consistency_score = check_bit_pattern_consistency(bits)
    % 비트 패턴의 일관성 검사 (AIS 특성 고려)
    
    if length(bits) < 8
        consistency_score = 0.5;
        return;
    end
    
    % 연속된 동일 비트 개수 검사 (AIS는 연속 6개 이상 금지)
    max_consecutive = 1;
    current_consecutive = 1;
    for i = 2:length(bits)
        if bits(i) == bits(i-1)
            current_consecutive = current_consecutive + 1;
            max_consecutive = max(max_consecutive, current_consecutive);
        else
            current_consecutive = 1;
        end
    end
    
    % 연속성 점수 (6개 이상이면 패널티)
    if max_consecutive >= 6
        consecutive_score = 0.1;
    elseif max_consecutive >= 5
        consecutive_score = 0.5;
    else
        consecutive_score = 1.0;
    end
    
    % 전체 일관성 점수
    consistency_score = consecutive_score;
end

%------------------------------------------------------------------------
% 고급 채널 추정 함수 (v3 개선)
function [h0, h1, bias, confidence] = estimate_channel_v3(received_signal, known_bits, prev_h0, prev_h1, prev_bias)
    % v3 고급 채널 추정 - 적응형 및 강건한 추정
    % received_signal: 수신된 신호 (실수값)
    % known_bits: 알려진 비트 패턴 (0, 1)
    % prev_h0, prev_h1, prev_bias: 이전 채널 계수들

    N = min(length(received_signal), length(known_bits));
    if N < 16
        h0 = prev_h0;
        h1 = prev_h1;
        bias = prev_bias;
        confidence = 0.1;
        return;
    end

    % 비트를 NRZI로 변환 후 -1, +1로 매핑
    nrzi_signal = zeros(size(known_bits));
    prev_nrzi = 0;
    for i = 1:length(known_bits)
        if known_bits(i) == 0
            nrzi_signal(i) = prev_nrzi;  % 0이면 이전 상태 유지
        else
            nrzi_signal(i) = 1 - prev_nrzi;  % 1이면 반전
        end
        prev_nrzi = nrzi_signal(i);
    end

    % 신호 레벨로 변환 (-1, +1)
    signal_levels = 2 * nrzi_signal - 1;

    % 채널 모델: y(n) = h0*x(n) + h1*x(n-1) + bias + noise
    % 최소자승법을 위한 행렬 구성
    A = zeros(N-1, 3);  % [h0, h1, bias]
    b = received_signal(2:N);

    for i = 1:N-1
        A(i, 1) = signal_levels(i+1);      % h0 * x(n)
        A(i, 2) = signal_levels(i);        % h1 * x(n-1)
        A(i, 3) = 1;                       % bias
    end

    % 강건한 최소자승법 (outlier 제거)
    try
        % 초기 추정
        coeffs = A \ b;

        % 잔차 계산 및 outlier 검출
        residuals = b - A * coeffs;
        residual_std = std(residuals);
        outlier_threshold = 2.5 * residual_std;

        % outlier가 아닌 데이터만 선택
        valid_indices = abs(residuals) <= outlier_threshold;

        if sum(valid_indices) >= 8  % 충분한 데이터가 있으면
            A_clean = A(valid_indices, :);
            b_clean = b(valid_indices);
            coeffs = A_clean \ b_clean;
        end

        h0_new = coeffs(1);
        h1_new = coeffs(2);
        bias_new = coeffs(3);

        % 채널 계수 유효성 검사 (조건 완화)
        if abs(h0_new) > 0.01 && abs(h0_new) < 10.0 && abs(h1_new) < 5.0
            % 신뢰도 계산 (잔차 기반)
            final_residuals = b_clean - A_clean * coeffs;
            mse = mean(final_residuals.^2);
            confidence = exp(-mse * 10);  % MSE가 낮을수록 높은 신뢰도

            h0 = h0_new;
            h1 = h1_new;
            bias = bias_new;
        else
            % 유효하지 않으면 이전 값 유지
            h0 = prev_h0;
            h1 = prev_h1;
            bias = prev_bias;
            confidence = 0.1;
        end

    catch
        % 오류 발생 시 이전 값 유지
        h0 = prev_h0;
        h1 = prev_h1;
        bias = prev_bias;
        confidence = 0.1;
    end

    % 신뢰도 범위 제한
    confidence = max(0.1, min(1.0, confidence));
end

%------------------------------------------------------------------------
% 적응형 노이즈 추정 함수 (v3 신규)
function noise_variance = estimate_noise_variance_adaptive(received_signal, window_size)
    % 적응형 노이즈 분산 추정

    if nargin < 2
        window_size = 32;
    end

    N = length(received_signal);
    if N < window_size
        noise_variance = var(received_signal) * 0.1;
        return;
    end

    % 이동 윈도우를 사용한 지역적 분산 계산
    local_variances = zeros(1, N - window_size + 1);
    for i = 1:N - window_size + 1
        window_data = received_signal(i:i+window_size-1);
        local_variances(i) = var(window_data);
    end

    % 최소 분산을 노이즈 분산으로 추정 (신호가 없는 구간)
    noise_variance = min(local_variances);

    % 최소값 제한
    noise_variance = max(0.001, noise_variance);
end

%------------------------------------------------------------------------
% GMSK 필터 설계 함수
function h = design_gmsk_filter(BT, OSR, span)
    % GMSK 펄스 성형 필터 설계

    if nargin < 3
        span = 4;
    end

    % 시간 벡터
    t = (-span*OSR/2:span*OSR/2) / OSR;

    % GMSK 임펄스 응답
    alpha = sqrt(2*log(2)) / (2*pi*BT);
    h = 0.5 * (erf(alpha*(t+0.5)) - erf(alpha*(t-0.5)));

    % 정규화
    h = h / sum(h);
end

%------------------------------------------------------------------------
% 프리앰블 생성 함수
function preamble = generate_ais_preamble()
    % AIS 프리앰블 패턴 생성 (24비트)
    preamble = repmat([0,1], 1, 12);  % 010101...010101 (24비트)
end

%------------------------------------------------------------------------
% 동기 패턴 생성 함수
function sync_pattern = generate_ais_sync_pattern()
    % AIS 동기 패턴 생성
    sync_pattern = [0,1,1,1,1,1,1,0];  % AIS 표준 동기 패턴
end

%------------------------------------------------------------------------
% CRC 계산 함수
function crc = calculate_crc16(data_bits)
    % CRC-16 계산 (AIS 표준)

    if isempty(data_bits)
        crc = 0;
        return;
    end

    crc = 0xFFFF;  % 초기값
    polynomial = 0x1021;  % CRC-16-CCITT 다항식

    for i = 1:length(data_bits)
        crc = bitxor(crc, bitshift(data_bits(i), 15));

        for j = 1:8
            if bitand(crc, 0x8000)
                crc = bitxor(bitshift(crc, 1), polynomial);
            else
                crc = bitshift(crc, 1);
            end
            crc = bitand(crc, 0xFFFF);
        end
    end

    crc = bitxor(crc, 0xFFFF);  % 최종 XOR
end

%------------------------------------------------------------------------
% 패킷 유효성 검사 함수
function is_valid = validate_ais_packet(bits)
    % AIS 패킷 유효성 검사

    is_valid = false;

    if length(bits) < 168  % 최소 AIS 패킷 길이
        return;
    end

    % 동기 패턴 검사
    sync_pattern = generate_ais_sync_pattern();
    if length(bits) >= length(sync_pattern)
        if isequal(bits(1:length(sync_pattern)), sync_pattern)
            is_valid = true;
        end
    end

    % 추가 검증 로직 (CRC 등)은 필요에 따라 구현
end

%------------------------------------------------------------------------
% 메인 처리 시작
%------------------------------------------------------------------------

fprintf('\n=== AIS 수신기 v3 - 고성능 Viterbi MLSD (PLL 제거) ===\n');
if ENABLE_VITERBI_MLSD
    fprintf('Viterbi MLSD: Enabled\n');
else
    fprintf('Viterbi MLSD: Disabled\n');
end
fprintf('Window Size: %d, Traceback Depth: %d\n', VITERBI_WINDOW_SIZE, VITERBI_TRACEBACK_DEPTH);
if ENABLE_CHANNEL_ESTIMATION
    fprintf('Channel Estimation: Enabled\n');
else
    fprintf('Channel Estimation: Disabled\n');
end
if VITERBI_SOFT_DECISION
    fprintf('Soft Decision: Enabled\n');
else
    fprintf('Soft Decision: Disabled\n');
end
if VITERBI_MULTI_HYPOTHESIS
    fprintf('Multi-Hypothesis: Enabled\n');
else
    fprintf('Multi-Hypothesis: Disabled\n');
end

%-------------------------------------------------------------------------
% 전처리
%-------------------------------------------------------------------------

% ADC 제한
if (ENABLE_ADC_LIMIT > 0)
    if (ENABLE_ADC_LIMIT == 1)
        G_pSrcDataCh1(G_pSrcDataCh1 < 0) = 0;
        G_pSrcDataCh1(G_pSrcDataCh1 > ADC_MAX_VALUE) = ADC_MAX_VALUE;
    else
        G_pSrcDataCh1(G_pSrcDataCh1 > ADC_MAX_VALUE) = ADC_MAX_VALUE;
    end
end

% DC 오프셋 제거
G_pSrcDataCh1 = G_pSrcDataCh1 - ADC_SUB_DC_OFFSET;

% GMSK 수신 필터 적용
if (ENABLE_GMSK_RX_FLT > 0)
    h_gmsk = design_gmsk_filter(RX_BT, OSR, 4);
    G_pFilteredData = conv(G_pSrcDataCh1, h_gmsk, 'same');
    fprintf('GMSK 필터 적용 완료\n');
else
    G_pFilteredData = G_pSrcDataCh1;
end

% 노치 필터 (필요시)
if (ENABLE_NOTCH_FLT > 0)
    % 노치 필터 구현 (생략)
    fprintf('노치 필터 적용 완료\n');
end

%-------------------------------------------------------------------------
% 프리앰블 및 동기 패턴 생성
%-------------------------------------------------------------------------
preamble_bits = generate_ais_preamble();
sync_pattern = generate_ais_sync_pattern();

% GMSK 변조된 프리앰블 생성 (상관 검출용)
h_tx = design_gmsk_filter(BT, OSR, 4);
preamble_upsampled = upsample(2*preamble_bits-1, OSR);  % -1, +1로 변환 후 업샘플링
G_vGmskPreamble = conv(preamble_upsampled, h_tx, 'same');

fprintf('프리앰블 길이: %d samples\n', length(G_vGmskPreamble));

%-------------------------------------------------------------------------
% Viterbi MLSD 관련 변수 초기화
%-------------------------------------------------------------------------
G_ViterbiEnabled = ENABLE_VITERBI_MLSD;
G_ChannelH0 = VITERBI_INITIAL_H0;
G_ChannelH1 = VITERBI_INITIAL_H1;
G_ChannelBias = VITERBI_INITIAL_BIAS;
G_NoiseVariance = VITERBI_NOISE_VARIANCE;

% 통계 변수들
G_ViterbiStats = struct();
G_ViterbiStats.total_packets = 0;
G_ViterbiStats.successful_packets = 0;
G_ViterbiStats.channel_estimations = 0;
G_ViterbiStats.avg_confidence = 0;
G_ViterbiStats.confidence_history = [];

% 채널 추적 변수들
G_ChannelHistory = struct();
G_ChannelHistory.h0 = [];
G_ChannelHistory.h1 = [];
G_ChannelHistory.bias = [];
G_ChannelHistory.confidence = [];

fprintf('Viterbi MLSD 초기화 완료\n');

%-------------------------------------------------------------------------
% 메인 처리 루프 - Viterbi MLSD 기반 패킷 검출
%-------------------------------------------------------------------------

% 상관 검출을 통한 프리앰블 검색
fprintf('\n프리앰블 검색 중...\n');

if (ENABLE_GMSK_RX_FLT > 0)
    correlation = conv(G_pFilteredData, flipud(G_vGmskPreamble), 'same');
else
    correlation = conv(G_pSrcDataCh1, flipud(G_vGmskPreamble), 'same');
end

% 피크 검출
correlation_abs = abs(correlation);
correlation_normalized = correlation_abs / max(correlation_abs);

% 적응형 임계값 설정
noise_floor = median(correlation_normalized);
adaptive_threshold = max(MAX_SYNC_CORRVAL, noise_floor + 0.3);

% 피크 찾기
[peaks, peak_indices] = findpeaks(correlation_normalized, 'MinPeakHeight', adaptive_threshold, ...
                                  'MinPeakDistance', length(G_vGmskPreamble));

fprintf('검출된 피크 수: %d (임계값: %.3f)\n', length(peaks), adaptive_threshold);

% 검출된 각 피크에 대해 Viterbi MLSD 처리
total_packets = 0;
successful_packets = 0;

for peak_idx = 1:length(peak_indices)
    sync_index = peak_indices(peak_idx);

    fprintf('\n--- 피크 %d/%d 처리 중 (위치: %d) ---\n', peak_idx, length(peak_indices), sync_index);

    % 채널 추정을 위한 프리앰블 신호 추출
    if ENABLE_CHANNEL_ESTIMATION
        preamble_start = max(1, sync_index - length(G_vGmskPreamble) + 1);
        preamble_end = min(length(G_pFilteredData), sync_index);

        if preamble_end > preamble_start
            if (ENABLE_GMSK_RX_FLT > 0)
                received_preamble = G_pFilteredData(preamble_start:preamble_end);
            else
                received_preamble = G_pSrcDataCh1(preamble_start:preamble_end);
            end

            % 고급 채널 추정 수행
            [new_h0, new_h1, new_bias, channel_confidence] = estimate_channel_v3(...
                received_preamble, preamble_bits, G_ChannelH0, G_ChannelH1, G_ChannelBias);

            % 채널 계수 업데이트 (신뢰도 기반)
            if channel_confidence > 0.1
                alpha = CHANNEL_UPDATE_RATE * channel_confidence;
                G_ChannelH0 = (1-alpha) * G_ChannelH0 + alpha * new_h0;
                G_ChannelH1 = (1-alpha) * G_ChannelH1 + alpha * new_h1;
                G_ChannelBias = (1-alpha) * G_ChannelBias + alpha * new_bias;

                G_ViterbiStats.channel_estimations = G_ViterbiStats.channel_estimations + 1;

                % 채널 히스토리 저장
                G_ChannelHistory.h0 = [G_ChannelHistory.h0, G_ChannelH0];
                G_ChannelHistory.h1 = [G_ChannelHistory.h1, G_ChannelH1];
                G_ChannelHistory.bias = [G_ChannelHistory.bias, G_ChannelBias];
                G_ChannelHistory.confidence = [G_ChannelHistory.confidence, channel_confidence];

                fprintf('채널 추정: h0=%.4f, h1=%.4f, bias=%.4f (신뢰도: %.3f)\n', ...
                    G_ChannelH0, G_ChannelH1, G_ChannelBias, channel_confidence);
            end
        end
    end

    % 적응형 노이즈 분산 추정
    if VITERBI_ADAPTIVE_NOISE
        data_start = max(1, sync_index - 50);
        data_end = min(length(G_pFilteredData), sync_index + 50);
        if (ENABLE_GMSK_RX_FLT > 0)
            noise_data = G_pFilteredData(data_start:data_end);
        else
            noise_data = G_pSrcDataCh1(data_start:data_end);
        end
        G_NoiseVariance = estimate_noise_variance_adaptive(noise_data, 32);
    end

    % 패킷 데이터 추출 (프리앰블 이후)
    packet_start = sync_index + 1;
    packet_end = min(length(G_pFilteredData), packet_start + VITERBI_WINDOW_SIZE - 1);

    if packet_end > packet_start
        if (ENABLE_GMSK_RX_FLT > 0)
            packet_signal = G_pFilteredData(packet_start:packet_end);
        else
            packet_signal = G_pSrcDataCh1(packet_start:packet_end);
        end

        % Viterbi MLSD 수행
        [detected_bits, path_metrics, viterbi_confidence] = viterbi_mlsd_v3(...
            packet_signal, G_ChannelH0, G_ChannelH1, G_ChannelBias, ...
            VITERBI_WINDOW_SIZE, VITERBI_TRACEBACK_DEPTH, G_NoiseVariance);

        total_packets = total_packets + 1;

        if ~isempty(detected_bits) && isscalar(viterbi_confidence) && viterbi_confidence > VITERBI_CONFIDENCE_THRESHOLD
            % 동기 패턴 검증
            if length(detected_bits) >= length(sync_pattern)
                sync_match = sum(detected_bits(1:length(sync_pattern)) == sync_pattern);
                sync_ratio = sync_match / length(sync_pattern);

                if sync_ratio >= 0.75  % 75% 이상 일치
                    successful_packets = successful_packets + 1;

                    % 신뢰도 히스토리 업데이트
                    G_ViterbiStats.confidence_history = [G_ViterbiStats.confidence_history, viterbi_confidence];

                    fprintf('패킷 검출 성공! 비트 수: %d, 신뢰도: %.3f, 동기 일치율: %.1f%%\n', ...
                        length(detected_bits), viterbi_confidence, sync_ratio*100);

                    % 패킷 유효성 추가 검사
                    if validate_ais_packet(detected_bits)
                        fprintf('AIS 패킷 유효성 검증 통과\n');
                    end
                else
                    fprintf('동기 패턴 불일치 (일치율: %.1f%%)\n', sync_ratio*100);
                end
            else
                fprintf('검출된 비트 수 부족: %d\n', length(detected_bits));
            end
        else
            if isempty(detected_bits)
                fprintf('Viterbi 검출 실패\n');
            else
                fprintf('신뢰도 부족: %.3f (임계값: %.3f)\n', viterbi_confidence, VITERBI_CONFIDENCE_THRESHOLD);
            end
        end
    end
end

% 통계 업데이트
G_ViterbiStats.total_packets = total_packets;
G_ViterbiStats.successful_packets = successful_packets;

if ~isempty(G_ViterbiStats.confidence_history)
    G_ViterbiStats.avg_confidence = mean(G_ViterbiStats.confidence_history);
end

%-------------------------------------------------------------------------
% 결과 출력
%-------------------------------------------------------------------------
fprintf('\n=== 처리 결과 ===\n');
fprintf('총 검출 시도: %d\n', total_packets);
fprintf('성공적 패킷: %d\n', successful_packets);
if total_packets > 0
    success_rate = successful_packets / total_packets * 100;
    fprintf('성공률: %.1f%%\n', success_rate);
end

fprintf('\n=== Viterbi MLSD 성능 ===\n');
fprintf('채널 추정 횟수: %d\n', G_ViterbiStats.channel_estimations);
fprintf('최종 채널 계수: h0=%.4f, h1=%.4f, bias=%.4f\n', G_ChannelH0, G_ChannelH1, G_ChannelBias);
fprintf('노이즈 분산: %.6f\n', G_NoiseVariance);

if ~isempty(G_ViterbiStats.confidence_history)
    fprintf('평균 신뢰도: %.3f\n', G_ViterbiStats.avg_confidence);
    fprintf('신뢰도 범위: %.3f ~ %.3f\n', min(G_ViterbiStats.confidence_history), max(G_ViterbiStats.confidence_history));
end

% 채널 추적 결과
if CHANNEL_TRACKING_ENABLE && length(G_ChannelHistory.h0) > 1
    fprintf('\n=== 채널 추적 결과 ===\n');
    fprintf('h0 변화: %.4f → %.4f\n', G_ChannelHistory.h0(1), G_ChannelHistory.h0(end));
    fprintf('h1 변화: %.4f → %.4f\n', G_ChannelHistory.h1(1), G_ChannelHistory.h1(end));
    fprintf('bias 변화: %.4f → %.4f\n', G_ChannelHistory.bias(1), G_ChannelHistory.bias(end));
end

fprintf('\n=== AIS 수신기 v3 처리 완료 ===\n');
fprintf('최종 패킷 수: %d\n', successful_packets);

%-------------------------------------------------------------------------
% 시각화 및 분석 (디버그 모드)
%-------------------------------------------------------------------------
if ENABLE_DEBUG || ENABLE_FREQ_ANALYSIS
    figure('Name', 'AIS 수신기 v3 - Viterbi MLSD 분석', 'Position', [100, 100, 1200, 800]);

    % 1. 상관 검출 결과
    if ENABLE_PLOT1
        subplot(3, 2, 1);
        plot(correlation_normalized);
        hold on;
        plot(peak_indices, peaks, 'ro', 'MarkerSize', 8);
        yline(adaptive_threshold, 'r--', 'LineWidth', 2);
        title('프리앰블 상관 검출');
        xlabel('샘플 인덱스');
        ylabel('정규화된 상관값');
        legend('상관값', '검출된 피크', '적응형 임계값');
        grid on;
    end

    % 2. 채널 계수 변화
    if ENABLE_PLOT2 && length(G_ChannelHistory.h0) > 1
        subplot(3, 2, 2);
        plot(G_ChannelHistory.h0, 'b-o', 'LineWidth', 2);
        hold on;
        plot(G_ChannelHistory.h1, 'r-s', 'LineWidth', 2);
        plot(G_ChannelHistory.bias, 'g-^', 'LineWidth', 2);
        title('채널 계수 추적');
        xlabel('추정 횟수');
        ylabel('계수 값');
        legend('h0', 'h1', 'bias');
        grid on;
    end

    % 3. 신뢰도 히스토리
    if ENABLE_PLOT3 && ~isempty(G_ViterbiStats.confidence_history)
        subplot(3, 2, 3);
        plot(G_ViterbiStats.confidence_history, 'b-o', 'LineWidth', 2);
        hold on;
        yline(VITERBI_CONFIDENCE_THRESHOLD, 'r--', 'LineWidth', 2);
        title('Viterbi 신뢰도 변화');
        xlabel('패킷 번호');
        ylabel('신뢰도');
        legend('신뢰도', '임계값');
        grid on;
    end

    % 4. 신호 스펙트럼 분석
    if ENABLE_PLOT4 && ENABLE_FREQ_ANALYSIS
        subplot(3, 2, 4);
        if (ENABLE_GMSK_RX_FLT > 0)
            [psd, freq] = pwelch(G_pFilteredData, [], [], [], BIT_RATE*OSR);
        else
            [psd, freq] = pwelch(G_pSrcDataCh1, [], [], [], BIT_RATE*OSR);
        end
        semilogy(freq, psd);
        title('신호 전력 스펙트럼 밀도');
        xlabel('주파수 (Hz)');
        ylabel('PSD');
        grid on;
    end

    % 5. 채널 추정 신뢰도
    if ENABLE_PLOT5 && ~isempty(G_ChannelHistory.confidence)
        subplot(3, 2, 5);
        plot(G_ChannelHistory.confidence, 'g-o', 'LineWidth', 2);
        title('채널 추정 신뢰도');
        xlabel('추정 횟수');
        ylabel('신뢰도');
        grid on;
    end

    % 6. 성능 요약
    if ENABLE_PLOT6
        subplot(3, 2, 6);
        categories = {'총 시도', '성공', '실패'};
        values = [total_packets, successful_packets, total_packets - successful_packets];
        bar(values);
        set(gca, 'XTickLabel', categories);
        title('패킷 검출 성능 요약');
        ylabel('패킷 수');
        for i = 1:length(values)
            text(i, values(i) + max(values)*0.02, num2str(values(i)), ...
                'HorizontalAlignment', 'center', 'FontWeight', 'bold');
        end
        grid on;
    end

    sgtitle('AIS 수신기 v3 - Viterbi MLSD 성능 분석');
end

%-------------------------------------------------------------------------
% 성능 비교 및 권장사항
%-------------------------------------------------------------------------
fprintf('\n=== 성능 분석 및 권장사항 ===\n');

if successful_packets >= 170
    fprintf('✓ 목표 성능 달성! (170+ 패킷)\n');
elseif successful_packets >= 150
    fprintf('△ 양호한 성능 (150+ 패킷)\n');
    fprintf('권장사항: VITERBI_WINDOW_SIZE 또는 TRACEBACK_DEPTH 증가 고려\n');
else
    fprintf('✗ 성능 개선 필요\n');
    fprintf('권장사항:\n');
    fprintf('  1. CHANNEL_UPDATE_RATE 조정 (현재: %.2f)\n', CHANNEL_UPDATE_RATE);
    fprintf('  2. VITERBI_CONFIDENCE_THRESHOLD 낮추기 (현재: %.2f)\n', VITERBI_CONFIDENCE_THRESHOLD);
    fprintf('  3. 노이즈 환경 개선 필요\n');
end

if G_ViterbiStats.channel_estimations > 0
    estimation_rate = G_ViterbiStats.channel_estimations / total_packets;
    if estimation_rate < 0.5
        fprintf('주의: 채널 추정 성공률 낮음 (%.1f%%)\n', estimation_rate*100);
    end
end

if ~isempty(G_ViterbiStats.confidence_history)
    low_confidence_count = sum(G_ViterbiStats.confidence_history < VITERBI_CONFIDENCE_THRESHOLD);
    if low_confidence_count > successful_packets * 0.3
        fprintf('주의: 낮은 신뢰도 검출이 많음 (%d개)\n', low_confidence_count);
    end
end

%-------------------------------------------------------------------------
% 파일 저장 (옵션)
%-------------------------------------------------------------------------
if ENABLE_FILE_SAVE
    % 결과 저장
    results = struct();
    results.total_packets = total_packets;
    results.successful_packets = successful_packets;
    results.viterbi_stats = G_ViterbiStats;
    results.channel_history = G_ChannelHistory;
    results.final_channel_coeffs = [G_ChannelH0, G_ChannelH1, G_ChannelBias];
    results.noise_variance = G_NoiseVariance;

    save('ais_receiver_v3_results.mat', 'results');
    fprintf('\n결과가 ais_receiver_v3_results.mat에 저장되었습니다.\n');
end

fprintf('\n프로그램 종료.\n');
